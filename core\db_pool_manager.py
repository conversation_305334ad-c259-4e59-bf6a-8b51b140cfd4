"""
数据库连接池管理器
实现动态连接池调整、连接监控、泄露检测等企业级功能
"""

import threading
import time
from dataclasses import dataclass
from typing import Any

from sqlalchemy import Engine
from sqlalchemy.pool import Pool

from config.settings import settings
from core.logging.logging_system import log as logger


@dataclass
class PoolStats:
    """连接池统计信息"""

    pool_size: int
    checked_in: int
    checked_out: int
    overflow: int
    invalid: int
    total_connections: int
    utilization_rate: float
    last_adjustment_time: float
    adjustment_count: int
    leak_detection_count: int


@dataclass
class ConnectionMetrics:
    """连接指标"""

    active_connections: int
    idle_connections: int
    overflow_connections: int
    pool_utilization: float
    connection_wait_time: float
    connection_creation_rate: float
    connection_error_rate: float
    timestamp: float


class DynamicConnectionPoolManager:
    """
    动态连接池管理器
    提供连接池动态调整、监控、泄露检测等功能
    """

    def __init__(self, engine: Engine):
        """
        初始化连接池管理器

        Args:
            engine: SQLAlchemy 引擎实例
        """
        self.engine = engine
        self.pool: Pool = engine.pool

        # 配置参数
        self.min_pool_size = settings.DB_POOL_MIN_SIZE
        self.max_pool_size = settings.DB_POOL_MAX_SIZE
        self.adjustment_interval = settings.DB_POOL_ADJUSTMENT_INTERVAL
        self.auto_adjust_enabled = settings.DB_POOL_AUTO_ADJUST
        self.leak_detection_enabled = settings.DB_POOL_LEAK_DETECTION_ENABLED
        self.leak_threshold = settings.DB_POOL_LEAK_THRESHOLD

        # 运行时状态
        self.current_pool_size = settings.DB_POOL_SIZE
        self.is_monitoring = False
        self.monitor_thread: threading.Thread | None = None
        self.adjustment_thread: threading.Thread | None = None
        self._lock = threading.Lock()

        # 统计信息
        self.stats = PoolStats(
            pool_size=self.current_pool_size,
            checked_in=0,
            checked_out=0,
            overflow=0,
            invalid=0,
            total_connections=0,
            utilization_rate=0.0,
            last_adjustment_time=time.perf_counter(),
            adjustment_count=0,
            leak_detection_count=0,
        )

        # 性能指标历史
        self.metrics_history = []
        self.max_history_size = 100

        logger.debug(
            f"DynamicConnectionPoolManager initialized: "
            f"pool_size={self.current_pool_size}, "
            f"min_size={self.min_pool_size}, "
            f"max_size={self.max_pool_size}",
        )

    def start_monitoring(self):
        """开始监控连接池"""
        if self.is_monitoring:
            logger.warning("Connection pool monitoring is already running")
            return

        self.is_monitoring = True

        # 启动监控线程
        if settings.DB_POOL_MONITOR_ENABLED:
            self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True, name="DBPoolMonitor")
            self.monitor_thread.start()
            logger.info("Database connection pool monitoring started")

        # 启动动态调整线程
        if self.auto_adjust_enabled:
            self.adjustment_thread = threading.Thread(target=self._adjustment_loop, daemon=True, name="DBPoolAdjuster")
            self.adjustment_thread.start()
            logger.info("Database connection pool auto-adjustment started")

    def stop_monitoring(self):
        """停止监控连接池"""
        if not self.is_monitoring:
            return

        self.is_monitoring = False

        # 等待线程结束
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        if self.adjustment_thread:
            self.adjustment_thread.join(timeout=5.0)

        logger.info("Database connection pool monitoring stopped")

    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                self._collect_metrics()

                # 连接泄露检测
                if self.leak_detection_enabled:
                    self._detect_connection_leaks()

                time.sleep(10)  # 每10秒监控一次

            except Exception as e:
                logger.error(f"Error in connection pool monitoring: {e}")
                time.sleep(10)

    def _adjustment_loop(self):
        """动态调整循环"""
        while self.is_monitoring:
            try:
                time.sleep(self.adjustment_interval)
                self._adjust_pool_size()

            except Exception as e:
                logger.error(f"Error in connection pool adjustment: {e}")
                time.sleep(self.adjustment_interval)

    def _collect_metrics(self):
        """收集连接池指标"""
        try:
            pool_status = self.get_pool_status()

            metrics = ConnectionMetrics(
                active_connections=pool_status["checked_out"],
                idle_connections=pool_status["checked_in"],
                overflow_connections=pool_status["overflow"],
                pool_utilization=pool_status["utilization_rate"],
                connection_wait_time=0.0,  # TODO: 实现等待时间统计
                connection_creation_rate=0.0,  # TODO: 实现创建速率统计
                connection_error_rate=0.0,  # TODO: 实现错误率统计
                timestamp=time.perf_counter(),
            )

            with self._lock:
                self.metrics_history.append(metrics)
                if len(self.metrics_history) > self.max_history_size:
                    self.metrics_history.pop(0)

                # 更新统计信息
                self.stats.pool_size = pool_status["pool_size"]
                self.stats.checked_in = pool_status["checked_in"]
                self.stats.checked_out = pool_status["checked_out"]
                self.stats.overflow = pool_status["overflow"]
                self.stats.invalid = pool_status["invalid"]
                self.stats.total_connections = pool_status["total_connections"]
                self.stats.utilization_rate = pool_status["utilization_rate"]

        except Exception as e:
            logger.error(f"Error collecting connection pool metrics: {e}")

    def get_pool_status(self) -> dict[str, Any]:
        """
        获取连接池状态

        Returns:
            Dict: 连接池状态信息
        """
        try:
            pool_size = self.pool.size()
            checked_in = self.pool.checkedin()
            checked_out = self.pool.checkedout()
            overflow = self.pool.overflow()
            # 某些连接池类型可能没有 invalid() 方法
            try:
                invalid = self.pool.invalid()
            except AttributeError:
                invalid = 0

            total_connections = checked_in + checked_out + overflow
            utilization_rate = checked_out / max(pool_size, 1) if pool_size > 0 else 0.0

            return {
                "pool_size": pool_size,
                "checked_in": checked_in,
                "checked_out": checked_out,
                "overflow": overflow,
                "invalid": invalid,
                "total_connections": total_connections,
                "utilization_rate": utilization_rate,
                "max_overflow": getattr(self.pool, "_max_overflow", 0),
                "timeout": getattr(self.pool, "_timeout", 0),
                "recycle": getattr(self.pool, "_recycle", 0),
            }
        except Exception as e:
            logger.error(f"Error getting pool status: {e}")
            return {
                "pool_size": 0,
                "checked_in": 0,
                "checked_out": 0,
                "overflow": 0,
                "invalid": 0,
            }

    def get_current_metrics(self) -> ConnectionMetrics | None:
        """获取当前连接指标"""
        with self._lock:
            if self.metrics_history:
                return self.metrics_history[-1]
        return None

    def get_stats(self) -> PoolStats:
        """获取连接池统计信息"""
        with self._lock:
            return self.stats

    def _adjust_pool_size(self):
        """动态调整连接池大小"""
        try:
            # pool_status = self.get_pool_status()
            # current_utilization = pool_status["utilization_rate"]

            # 获取最近的性能指标
            recent_metrics = self._get_recent_metrics(window_size=5)
            if not recent_metrics:
                return

            avg_utilization = sum(m.pool_utilization for m in recent_metrics) / len(recent_metrics)

            # 调整策略
            target_size = self.current_pool_size

            if avg_utilization > 0.8:  # 使用率过高，增加连接
                target_size = min(self.max_pool_size, self.current_pool_size + 5)
            elif avg_utilization < 0.3:  # 使用率过低，减少连接
                target_size = max(self.min_pool_size, self.current_pool_size - 2)

            if target_size != self.current_pool_size:
                self._resize_pool(target_size)

        except Exception as e:
            logger.error(f"Error adjusting pool size: {e}")

    def _resize_pool(self, new_size: int):
        """调整连接池大小"""
        try:
            old_size = self.current_pool_size

            # 注意：SQLAlchemy 的连接池大小在运行时不能直接修改
            # 这里记录调整意图，实际调整需要重新创建引擎
            logger.info(f"Pool size adjustment requested: {old_size} -> {new_size}")

            with self._lock:
                self.current_pool_size = new_size
                self.stats.last_adjustment_time = time.perf_counter()
                self.stats.adjustment_count += 1

            # TODO: 实现实际的连接池大小调整
            # 这需要重新创建引擎或使用支持动态调整的连接池实现

        except Exception as e:
            logger.error(f"Error resizing pool: {e}")

    def _detect_connection_leaks(self):
        """检测连接泄露"""
        try:
            pool_status = self.get_pool_status()
            utilization = pool_status["utilization_rate"]

            if utilization > self.leak_threshold:
                # 可能存在连接泄露
                logger.warning(
                    f"Potential connection leak detected: "
                    f"utilization={utilization:.2f}, "
                    f"threshold={self.leak_threshold}",
                )

                with self._lock:
                    self.stats.leak_detection_count += 1

                # 记录详细信息用于分析
                self._log_pool_details()

        except Exception as e:
            logger.error(f"Error detecting connection leaks: {e}")

    def _log_pool_details(self):
        """记录连接池详细信息"""
        try:
            pool_status = self.get_pool_status()
            logger.info(f"Pool details: {pool_status}")

            # 记录最近的连接活动
            recent_metrics = self._get_recent_metrics(window_size=10)
            if recent_metrics:
                avg_utilization = sum(m.pool_utilization for m in recent_metrics) / len(recent_metrics)
                logger.info(f"Recent average utilization: {avg_utilization:.2f}")

        except Exception as e:
            logger.error(f"Error logging pool details: {e}")

    def _get_recent_metrics(self, window_size: int = 10) -> list:
        """获取最近的性能指标"""
        with self._lock:
            if len(self.metrics_history) <= window_size:
                return self.metrics_history.copy()
            else:
                return self.metrics_history[-window_size:].copy()

    def warm_up_pool(self, target_connections: int = None):
        """
        预热连接池，创建初始连接

        Args:
            target_connections: 目标连接数，默认为配置的预热连接数
        """
        try:
            target = target_connections or settings.DB_POOL_WARMUP_CONNECTIONS
            logger.info(f"Warming up connection pool with {target} connections")

            connections = []
            for i in range(target):
                try:
                    conn = self.engine.connect()
                    connections.append(conn)
                except Exception as e:
                    logger.warning(f"Failed to create warmup connection {i + 1}: {e}")
                    break

            # 归还连接到池中
            for conn in connections:
                try:
                    conn.close()
                except Exception as e:
                    logger.warning(f"Failed to close warmup connection: {e}")

            logger.info(f"Connection pool warmup completed: {len(connections)} connections created")

        except Exception as e:
            logger.error(f"Error warming up connection pool: {e}")

    def graceful_shutdown(self, timeout: int = 30):
        """
        优雅关闭连接池

        Args:
            timeout: 等待超时时间（秒）
        """
        try:
            logger.info("Starting graceful shutdown of connection pool")

            # 停止监控
            self.stop_monitoring()

            # 等待活跃连接完成
            start_time = time.perf_counter()
            while time.perf_counter() - start_time < timeout:
                pool_status = self.get_pool_status()
                if pool_status["checked_out"] == 0:
                    break
                logger.info(f"Waiting for {pool_status['checked_out']} active connections to finish")
                time.sleep(1)

            # 释放连接池
            self.engine.dispose()
            logger.info("Connection pool graceful shutdown completed")

        except Exception as e:
            logger.error(f"Error during graceful shutdown: {e}")

    def force_pool_recreation(self):
        """强制重新创建连接池（用于修复连接泄露）"""
        try:
            logger.warning("Forcing connection pool recreation due to potential leaks")

            # 记录当前状态
            old_status = self.get_pool_status()

            # 释放当前连接池
            self.engine.dispose()

            # 重新创建连接池会在下次访问时自动进行
            logger.info(f"Pool recreation completed. Old status: {old_status}")

        except Exception as e:
            logger.error(f"Error forcing pool recreation: {e}")


# 全局连接池管理器实例
pool_manager: DynamicConnectionPoolManager | None = None


def get_pool_manager() -> DynamicConnectionPoolManager | None:
    """获取全局连接池管理器实例"""
    return pool_manager


def set_pool_manager(manager: DynamicConnectionPoolManager):
    """设置全局连接池管理器实例"""
    global pool_manager
    pool_manager = manager
