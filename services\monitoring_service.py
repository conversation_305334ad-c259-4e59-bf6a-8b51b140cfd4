"""
监控服务
整合指标收集、告警管理和系统监控，提供统一的监控服务接口
"""

import asyncio
import time
from dataclasses import dataclass
from typing import Any

from core.logging.logging_system import log as logger
from core.metrics_collector import MetricCategory, metrics_collector
from services.alert_manager import alert_manager
from services.service_degradation_manager import service_degradation_manager


@dataclass
class SystemHealthStatus:
    """系统健康状态"""

    overall_status: str  # healthy, degraded, critical
    cpu_status: str
    memory_status: str
    database_status: str
    registration_status: str
    degradation_status: str
    active_alerts_count: int
    last_updated: float


@dataclass
class MonitoringDashboard:
    """监控仪表板数据"""

    system_health: SystemHealthStatus
    key_metrics: dict[str, Any]
    recent_alerts: list[dict[str, Any]]
    performance_trends: dict[str, list[float]]
    service_status: dict[str, str]


class MonitoringService:
    """
    监控服务

    提供系统监控、健康检查、指标聚合和监控仪表板功能
    """

    def __init__(self):
        """初始化监控服务"""
        self.is_running = False
        self.collection_task: asyncio.Task | None = None
        self.health_check_task: asyncio.Task | None = None

        # 健康状态阈值
        self.health_thresholds = {
            "cpu_warning": 70.0,
            "cpu_critical": 85.0,
            "memory_warning": 75.0,
            "memory_critical": 90.0,
            "error_rate_warning": 0.02,  # 2%
            "error_rate_critical": 0.05,  # 5%
            "success_rate_warning": 0.95,  # 95%
            "success_rate_critical": 0.90,  # 90%
        }

        logger.debug("MonitoringService initialized")

    async def start(self):
        """启动监控服务"""
        if self.is_running:
            logger.warning("MonitoringService is already running")
            return

        self.is_running = True

        # 启动指标收集任务
        self.collection_task = asyncio.create_task(self._metrics_collection_loop())

        # 启动健康检查任务
        self.health_check_task = asyncio.create_task(self._health_check_loop())

        # 启动告警管理器
        await alert_manager.start()

        logger.info("MonitoringService started")

    async def stop(self):
        """停止监控服务"""
        if not self.is_running:
            return

        self.is_running = False

        # 停止任务
        if self.collection_task:
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass

        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass

        # 停止告警管理器
        await alert_manager.stop()

        logger.info("MonitoringService stopped")

    async def _metrics_collection_loop(self):
        """指标收集循环"""
        while self.is_running:
            try:
                # 收集系统指标
                metrics_collector.collect_system_metrics()

                # 收集业务指标
                await self._collect_business_metrics()

                # 收集降级策略指标
                await self._collect_degradation_metrics()

                await asyncio.sleep(30)  # 每30秒收集一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
                await asyncio.sleep(30)

    async def _health_check_loop(self):
        """健康检查循环"""
        while self.is_running:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(60)  # 每分钟检查一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(60)

    async def _collect_business_metrics(self):
        """收集业务指标"""
        try:
            # 这里可以从数据库或其他业务系统收集指标
            # 暂时使用模拟数据

            # 模拟注册任务统计
            # 在实际实现中，这些数据应该从数据库或业务系统获取
            pass

        except Exception as e:
            logger.error(f"Error collecting business metrics: {e}")

    async def _collect_degradation_metrics(self):
        """收集降级策略指标"""
        try:
            if hasattr(service_degradation_manager, "get_status_info"):
                status_info = service_degradation_manager.get_status_info()

                # 服务状态 (0=健康, 1=降级, 2=失败)
                status_mapping = {"healthy": 0, "degraded": 1, "failed": 2}
                status_value = status_mapping.get(status_info.get("service_status", "healthy"), 0)
                metrics_collector.set_gauge("degradation.status", status_value)

                # 缓存大小
                cache_info = status_info.get("cache_info", {})
                cache_size = cache_info.get("cached_items", 0)
                metrics_collector.set_gauge("degradation.cache.size", cache_size)

                # 熔断器状态 (0=关闭, 1=半开, 2=开启)
                cb_state_mapping = {"closed": 0, "half_open": 1, "open": 2}
                cb_state = status_info.get("circuit_breaker_state", "closed")
                cb_value = cb_state_mapping.get(cb_state, 0)
                metrics_collector.set_gauge("degradation.circuit.breaker.state", cb_value)

        except Exception as e:
            logger.error(f"Error collecting degradation metrics: {e}")

    async def _perform_health_checks(self):
        """执行健康检查"""
        try:
            health_status = await self.get_system_health()

            # 记录健康状态指标
            status_mapping = {"healthy": 1, "degraded": 0.5, "critical": 0}
            health_value = status_mapping.get(health_status.overall_status, 0)
            metrics_collector.set_gauge("system.health.status", health_value)

            logger.debug(f"Health check completed: {health_status.overall_status}")

        except Exception as e:
            logger.error(f"Error performing health checks: {e}")

    async def get_system_health(self) -> SystemHealthStatus:
        """获取系统健康状态"""
        current_time = time.perf_counter()

        # 获取关键指标
        cpu_usage = metrics_collector.get_metric_current_value("system.cpu.usage") or 0
        memory_usage = metrics_collector.get_metric_current_value("system.memory.usage") or 0
        db_utilization = metrics_collector.get_metric_current_value("database.pool.utilization") or 0
        success_rate = metrics_collector.get_metric_current_value("registration.tasks.success.rate") or 1.0
        degradation_status = metrics_collector.get_metric_current_value("degradation.status") or 0

        # 评估各组件状态
        cpu_status = self._evaluate_status(
            cpu_usage, self.health_thresholds["cpu_warning"], self.health_thresholds["cpu_critical"]
        )
        memory_status = self._evaluate_status(
            memory_usage, self.health_thresholds["memory_warning"], self.health_thresholds["memory_critical"]
        )
        database_status = self._evaluate_status(db_utilization, 80.0, 95.0)
        registration_status = self._evaluate_status(
            1.0 - success_rate,
            1.0 - self.health_thresholds["success_rate_warning"],
            1.0 - self.health_thresholds["success_rate_critical"],
        )

        # 降级状态评估
        if degradation_status == 0:
            degradation_status_str = "healthy"
        elif degradation_status == 1:
            degradation_status_str = "degraded"
        else:
            degradation_status_str = "critical"

        # 获取活跃告警数量
        active_alerts = alert_manager.get_active_alerts()
        active_alerts_count = len(active_alerts)

        # 评估整体状态
        component_statuses = [cpu_status, memory_status, database_status, registration_status]
        if "critical" in component_statuses or degradation_status_str == "critical":
            overall_status = "critical"
        elif "degraded" in component_statuses or degradation_status_str == "degraded":
            overall_status = "degraded"
        else:
            overall_status = "healthy"

        return SystemHealthStatus(
            overall_status=overall_status,
            cpu_status=cpu_status,
            memory_status=memory_status,
            database_status=database_status,
            registration_status=registration_status,
            degradation_status=degradation_status_str,
            active_alerts_count=active_alerts_count,
            last_updated=current_time,
        )

    def _evaluate_status(self, value: float, warning_threshold: float, critical_threshold: float) -> str:
        """评估状态"""
        if value >= critical_threshold:
            return "critical"
        elif value >= warning_threshold:
            return "degraded"
        else:
            return "healthy"

    async def get_monitoring_dashboard(self) -> MonitoringDashboard:
        """获取监控仪表板数据"""
        # 获取系统健康状态
        system_health = await self.get_system_health()

        # 获取关键指标
        key_metrics = {
            "cpu_usage": metrics_collector.get_metric_current_value("system.cpu.usage"),
            "memory_usage": metrics_collector.get_metric_current_value("system.memory.usage"),
            "memory_available": metrics_collector.get_metric_current_value("system.memory.available"),
            "queue_length": metrics_collector.get_metric_current_value("system.queue.length"),
            "db_pool_utilization": metrics_collector.get_metric_current_value("database.pool.utilization"),
            "registration_success_rate": metrics_collector.get_metric_current_value("registration.tasks.success.rate"),
            "processing_time": metrics_collector.get_metric_current_value("performance.processing.time"),
            "throughput": metrics_collector.get_metric_current_value("performance.throughput"),
        }

        # 获取最近告警
        recent_alerts = []
        for alert in alert_manager.get_alert_history(limit=10):
            recent_alerts.append(
                {
                    "rule_name": alert.rule_name,
                    "severity": alert.severity.value,
                    "status": alert.status.value,
                    "message": alert.message,
                    "created_at": alert.created_at,
                    "updated_at": alert.updated_at,
                }
            )

        # 获取性能趋势（最近1小时的数据）
        performance_trends = {}
        trend_metrics = ["system.cpu.usage", "system.memory.usage", "performance.throughput"]
        for metric_name in trend_metrics:
            history = metrics_collector.get_metric_history(metric_name, limit=60)  # 最近60个数据点
            values = [point.value for point in history]
            performance_trends[metric_name] = values

        # 获取服务状态
        service_status = {
            "monitoring_service": "running" if self.is_running else "stopped",
            "alert_manager": "running" if alert_manager.is_running else "stopped",
            "degradation_manager": 
                "running"
                if hasattr(service_degradation_manager, "is_running") and service_degradation_manager.is_running
                else "unknown",
            "metrics_collector": "active",
        }

        return MonitoringDashboard(
            system_health=system_health,
            key_metrics=key_metrics,
            recent_alerts=recent_alerts,
            performance_trends=performance_trends,
            service_status=service_status,
        )

    def get_metrics_summary(self, category: MetricCategory | None = None) -> dict[str, Any]:
        """获取指标摘要"""
        if category:
            return {
                "category": category.value,
                "metrics": metrics_collector.get_metrics_by_category(category)
            }
        else:
            return metrics_collector.get_all_metrics_summary()

    def get_alert_statistics(self) -> dict[str, Any]:
        """获取告警统计"""
        return alert_manager.get_alert_statistics()

    async def trigger_manual_health_check(self) -> SystemHealthStatus:
        """手动触发健康检查"""
        logger.info("Manual health check triggered")
        return await self.get_system_health()

    async def collect_metrics_now(self):
        """立即收集指标"""
        logger.info("Manual metrics collection triggered")
        metrics_collector.collect_system_metrics()
        await self._collect_business_metrics()
        await self._collect_degradation_metrics()


# 全局监控服务实例
monitoring_service = MonitoringService()
