"""
日志堆栈信息增强器
自动为 ERROR 及以上级别的日志添加完整的异常堆栈信息

功能特性：
- 智能检测异常和调用堆栈
- 美观的格式化输出
- 性能优化的缓存机制
- 可配置的过滤和深度控制
"""

import inspect
import sys
import threading
import time
import traceback
from typing import Any

from config.settings import settings


class StackEnhancer:
    """
    堆栈信息增强器

    负责智能获取、格式化和缓存异常堆栈信息，
    为日志系统提供详细的错误上下文。
    """

    def __init__(self):
        """初始化堆栈增强器"""
        self.enabled = settings.LOG_STACK_ENHANCEMENT_ENABLED
        self.max_depth = settings.LOG_STACK_MAX_DEPTH
        self.filter_internal = settings.LOG_STACK_FILTER_INTERNAL
        self.min_level = settings.LOG_STACK_MIN_LEVEL.upper()
        self.cache_size = settings.LOG_STACK_CACHE_SIZE
        self.cache_ttl = settings.LOG_STACK_CACHE_TTL

        # 缓存相关
        self._cache = {}
        self._cache_lock = threading.Lock()
        self._last_cleanup = time.time()

        # 级别映射
        self._level_mapping = {"DEBUG": 10, "INFO": 20, "WARNING": 30, "ERROR": 40, "CRITICAL": 50}

        self._min_level_no = self._level_mapping.get(self.min_level, 40)

    def enhance_log_record(self, record) -> bool:
        """
        增强日志记录，添加堆栈信息

        Args:
            record: loguru 日志记录对象

        Returns:
            bool: 始终返回 True，表示允许记录日志
        """
        if not self.enabled:
            return True

        try:
            # 检查日志级别 - loguru 的 record["level"] 是一个对象，有 no 属性
            level_no = record["level"].no if hasattr(record["level"], "no") else 0
            if level_no < self._min_level_no:
                return True

            # 获取堆栈信息
            stack_info = self._get_stack_info()
            if stack_info:
                # 将堆栈信息添加到消息中
                original_message = record["message"]
                enhanced_message = f"{original_message}\n\n{stack_info}"
                record["message"] = enhanced_message

        except Exception as e:
            # 如果增强失败，添加警告但不阻止日志记录
            try:
                original_message = record.get("message", "")
                record["message"] = f"{original_message}\n⚠️ 堆栈信息获取失败: {str(e)}"
            except Exception:
                # 如果连错误处理都失败，至少确保日志能记录
                pass

        return True

    def _get_stack_info(self) -> str | None:
        """
        获取格式化的堆栈信息

        Returns:
            Optional[str]: 格式化的堆栈信息
        """
        try:
            # 获取堆栈信息
            stack_info = None

            # 首先尝试获取异常堆栈
            exception_stack = self._get_exception_stack()
            if exception_stack:
                stack_info = self._format_exception_stack(exception_stack)
            else:
                # 如果没有异常，获取调用堆栈
                call_stack = self._get_call_stack()
                if call_stack:
                    stack_info = self._format_call_stack(call_stack)

            return stack_info

        except Exception as e:
            return f"⚠️ 获取堆栈信息时发生错误: {str(e)}"

    def _get_exception_stack(self) -> str | None:
        """获取当前异常的堆栈信息"""
        try:
            exc_info = sys.exc_info()
            if exc_info[0] is not None:
                # 有活跃的异常
                return traceback.format_exc()

            # 尝试从 traceback 模块获取
            exc_text = traceback.format_exc()
            if exc_text and exc_text.strip() != "NoneType: None":
                return exc_text

        except Exception:
            pass
        return None

    def _get_call_stack(self) -> list[inspect.FrameInfo] | None:
        """获取调用堆栈信息"""
        try:
            stack = inspect.stack()
            # 过滤掉日志系统内部的调用
            filtered_stack = []

            for frame_info in stack[1 : self.max_depth + 1]:
                if self.filter_internal and self._is_internal_frame(frame_info):
                    continue
                filtered_stack.append(frame_info)

            return filtered_stack if filtered_stack else None

        except Exception:
            return None

    def _is_internal_frame(self, frame_info: inspect.FrameInfo) -> bool:
        """判断是否为内部框架调用"""
        try:
            filename = frame_info.filename.lower()
            function_name = frame_info.function.lower()

            # 内部路径和函数名模式
            internal_patterns = [
                "logging",
                "loguru",
                "traceback",
                "inspect",
                "stack_enhancer",
                "logging_system",
                "enhanced_formatter",
                "error_enhancement",
                "_log",
                "emit",
                "handle",
            ]

            return any(pattern in filename or pattern in function_name for pattern in internal_patterns)

        except Exception:
            return False

    def _format_exception_stack(self, exception_text: str) -> str:
        """格式化异常堆栈信息"""
        lines = exception_text.strip().split("\n")
        formatted_lines = []

        formatted_lines.append("📋 异常堆栈信息:")

        for i, line in enumerate(lines):
            if line.strip():
                if i == 0:
                    prefix = "  🔍 "
                elif i < len(lines) - 1:
                    prefix = "  ├─ "
                else:
                    prefix = "  └─ "
                formatted_lines.append(f"{prefix}{line.strip()}")

        return "\n".join(formatted_lines)

    def _format_call_stack(self, stack_frames: list[inspect.FrameInfo]) -> str:
        """格式化调用堆栈信息"""
        if not stack_frames:
            return ""

        formatted_lines = ["📋 调用堆栈信息:"]

        for i, frame in enumerate(stack_frames):
            try:
                # 获取文件名（只保留最后两级路径）
                filename_parts = frame.filename.replace("\\", "/").split("/")
                short_filename = "/".join(filename_parts[-2:]) if len(filename_parts) > 1 else filename_parts[-1]

                location = f"{short_filename}:{frame.lineno}"
                function = f"{frame.function}()"

                prefix = "  ├─ " if i < len(stack_frames) - 1 else "  └─ "
                formatted_lines.append(f"{prefix}{location} in {function}")

                # 添加代码上下文（如果可用）
                if hasattr(frame, "code_context") and frame.code_context:
                    code_line = frame.code_context[0].strip()
                    if code_line and len(code_line) < 100:  # 避免过长的代码行
                        formatted_lines.append(f"      💡 {code_line}")

            except Exception:
                # 如果处理某个帧失败，跳过但继续处理其他帧
                continue

        return "\n".join(formatted_lines)

    def _get_from_cache(self, cache_key: str) -> str | None:
        """从缓存获取结果"""
        try:
            with self._cache_lock:
                # 清理过期缓存
                self._cleanup_cache()

                cache_entry = self._cache.get(cache_key)
                if cache_entry:
                    timestamp, value = cache_entry
                    if time.time() - timestamp < self.cache_ttl:
                        return value
                    else:
                        # 过期，删除
                        del self._cache[cache_key]
        except Exception:
            pass
        return None

    def _put_to_cache(self, cache_key: str, value: str):
        """将结果放入缓存"""
        try:
            with self._cache_lock:
                # 检查缓存大小
                if len(self._cache) >= self.cache_size:
                    # 删除最旧的条目
                    oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k][0])
                    del self._cache[oldest_key]

                self._cache[cache_key] = (time.time(), value)
        except Exception:
            pass

    def _cleanup_cache(self):
        """清理过期的缓存条目"""
        try:
            current_time = time.time()

            # 每5分钟清理一次
            if current_time - self._last_cleanup < 300:
                return

            expired_keys = []
            for key, (timestamp, _) in self._cache.items():
                if current_time - timestamp >= self.cache_ttl:
                    expired_keys.append(key)

            for key in expired_keys:
                del self._cache[key]

            self._last_cleanup = current_time
        except Exception:
            pass

    def get_cache_stats(self) -> dict[str, Any]:
        """获取缓存统计信息"""
        try:
            with self._cache_lock:
                return {
                    "cache_size": len(self._cache),
                    "max_cache_size": self.cache_size,
                    "cache_ttl": self.cache_ttl,
                    "last_cleanup": self._last_cleanup,
                    "enabled": self.enabled,
                }
        except Exception:
            return {"error": "无法获取缓存统计"}

    def clear_cache(self):
        """清空缓存"""
        try:
            with self._cache_lock:
                self._cache.clear()
                self._last_cleanup = time.time()
        except Exception:
            pass


# 全局堆栈增强器实例
stack_enhancer = StackEnhancer()
