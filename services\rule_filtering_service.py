"""
规则筛选服务
基于患者数据特征快速过滤适用规则，减少不必要的校验
"""

import time
from collections import defaultdict
from dataclasses import dataclass
from typing import Any

from core.logging.logging_system import log as logger
from core.rule_cache import RULE_CACHE


@dataclass
class RuleFilterStats:
    """规则筛选统计"""

    original_rule_count: int
    filtered_rule_count: int
    filter_time_ms: float
    reduction_percentage: float
    filter_reason_counts: dict[str, int]


class RuleApplicabilityFilter:
    """
    规则适用性筛选器
    基于患者数据特征快速判断规则是否需要执行
    """

    def __init__(self):
        """初始化筛选器"""
        self.rule_metadata_cache = {}  # 规则元数据缓存
        self.filter_stats = defaultdict(int)

        # 预构建规则适用性索引
        self._rule_applicability_index = {
            "gender_specific": {},  # 性别特定规则
            "age_specific": {},  # 年龄特定规则
            "diagnosis_dependent": {},  # 诊断依赖规则
            "medication_specific": {},  # 药品特定规则
            "department_specific": {},  # 科室特定规则
        }

        self._index_built = False

        # 如果启用了预构建，立即构建索引
        from config.settings import settings

        if getattr(settings, "ENABLE_INDEX_PREBUILD", True):
            try:
                self._build_rule_applicability_index()
                self._index_built = True
            except Exception as e:
                logger.warning(f"初始化时构建索引失败: {e}")

        logger.debug("规则适用性筛选器初始化完成")

    def _build_rule_applicability_index(self):
        """构建规则适用性索引"""
        start_time = time.perf_counter()

        for rule_id, rule_instance in RULE_CACHE.items():
            try:
                # 分析规则属性构建索引
                self._analyze_rule_applicability(rule_id, rule_instance)
            except Exception as e:
                logger.warning(f"分析规则 {rule_id} 适用性失败: {e}")

        build_time = (time.perf_counter() - start_time) * 1000
        logger.debug(f"规则适用性索引构建完成，耗时 {build_time:.2f}ms，共索引 {len(RULE_CACHE)} 条规则")

    def _analyze_rule_applicability(self, rule_id: str, rule_instance):
        """分析单个规则的适用性条件"""
        rule_name = rule_instance.__class__.__name__.lower()

        # 性别特定规则
        if "male" in rule_name or "female" in rule_name:
            gender = "M" if "male" in rule_name else "F"
            if gender not in self._rule_applicability_index["gender_specific"]:
                self._rule_applicability_index["gender_specific"][gender] = []
            self._rule_applicability_index["gender_specific"][gender].append(rule_id)

        # 年龄特定规则
        if any(keyword in rule_name for keyword in ["pediatric", "child", "infant", "elderly", "adult"]):
            age_group = self._extract_age_group(rule_name)
            if age_group not in self._rule_applicability_index["age_specific"]:
                self._rule_applicability_index["age_specific"][age_group] = []
            self._rule_applicability_index["age_specific"][age_group].append(rule_id)

        # 检查规则实例的属性
        if hasattr(rule_instance, "yb_codes") and rule_instance.yb_codes:
            # 药品特定规则
            for code in rule_instance.yb_codes:
                if code not in self._rule_applicability_index["medication_specific"]:
                    self._rule_applicability_index["medication_specific"][code] = []
                self._rule_applicability_index["medication_specific"][code].append(rule_id)

        if hasattr(rule_instance, "diagnosis_codes") and rule_instance.diagnosis_codes:
            # 诊断依赖规则
            for code in rule_instance.diagnosis_codes:
                if code not in self._rule_applicability_index["diagnosis_dependent"]:
                    self._rule_applicability_index["diagnosis_dependent"][code] = []
                self._rule_applicability_index["diagnosis_dependent"][code].append(rule_id)

    def _extract_age_group(self, rule_name: str) -> str:
        """从规则名称提取年龄组"""
        if any(keyword in rule_name for keyword in ["pediatric", "child", "infant"]):
            return "pediatric"  # 0-18岁
        elif "elderly" in rule_name:
            return "elderly"  # >65岁
        else:
            return "adult"  # 18-65岁

    def filter_applicable_rules(self, patient_data, rule_ids: list[str]) -> tuple[list[str], RuleFilterStats]:
        """
        筛选适用的规则

        Args:
            patient_data: 优化的患者数据
            rule_ids: 候选规则ID列表

        Returns:
            tuple: (适用规则列表, 筛选统计)
        """
        start_time = time.perf_counter()
        applicable_rules = set()
        filter_reasons = defaultdict(int)

        # 获取患者特征
        patient_features = self._extract_patient_features(patient_data)

        for rule_id in rule_ids:
            if self._is_rule_applicable(rule_id, patient_features, filter_reasons):
                applicable_rules.add(rule_id)

        # 计算统计信息
        filter_time = (time.perf_counter() - start_time) * 1000
        filtered_count = len(applicable_rules)
        reduction = (1 - filtered_count / len(rule_ids)) * 100 if rule_ids else 0

        stats = RuleFilterStats(
            original_rule_count=len(rule_ids),
            filtered_rule_count=filtered_count,
            filter_time_ms=filter_time,
            reduction_percentage=reduction,
            filter_reason_counts=dict(filter_reasons),
        )

        logger.debug(
            f"规则筛选完成：{len(rule_ids)} -> {filtered_count} 条规则"
            f"（减少 {reduction:.1f}%），耗时 {filter_time:.2f}ms"
        )

        return list(applicable_rules), stats

    def _extract_patient_features(self, patient_data) -> dict[str, Any]:
        """提取患者特征用于规则筛选"""
        # 检查是否为超优化数据
        if hasattr(patient_data, "quick_features"):
            # UltraOptimizedPatientData - 使用预计算特征
            features = {
                "gender": patient_data.get_feature_fast("gender"),
                "age": patient_data.get_feature_fast("age"),
                "diagnosis_codes": list(patient_data.diagnosis_codes_set),
                "medication_codes": list(patient_data.ultra_fee_index._by_ybdm.keys()),
                "has_fees": patient_data.get_feature_fast("has_fees"),
            }

            # 年龄组分类
            features["age_group"] = patient_data.get_feature_fast("age_group")
        else:
            # OptimizedPatientData - 使用原有逻辑
            features = {
                "gender": patient_data.basic_information.gender,
                "age": patient_data.basic_information.age,
                "diagnosis_codes": patient_data.get_diagnosis_codes(),
                "medication_codes": patient_data.fee_index.get_unique_ybdm(),
                "has_fees": len(patient_data.fee_index.get_all()) > 0,
            }

            # 年龄组分类
            if features["age"] is not None:
                if features["age"] < 18:
                    features["age_group"] = "pediatric"
                elif features["age"] > 65:
                    features["age_group"] = "elderly"
                else:
                    features["age_group"] = "adult"
            else:
                features["age_group"] = "unknown"

        return features

    def _is_rule_applicable(
        self, rule_id: str, patient_features: dict[str, Any], filter_reasons: dict[str, int]
    ) -> bool:
        """
        判断规则是否适用于患者

        Args:
            rule_id: 规则ID
            patient_features: 患者特征
            filter_reasons: 筛选原因统计

        Returns:
            bool: 是否适用
        """

        # 1. 性别特定规则检查
        for gender, gender_rules in self._rule_applicability_index["gender_specific"].items():
            if rule_id in gender_rules and patient_features["gender"] != gender:
                filter_reasons["gender_mismatch"] += 1
                return False

        # 2. 年龄特定规则检查
        patient_age_group = patient_features.get("age_group", "unknown")
        for age_group, age_rules in self._rule_applicability_index["age_specific"].items():
            if rule_id in age_rules and patient_age_group != age_group:
                filter_reasons["age_group_mismatch"] += 1
                return False

        # 3. 诊断依赖规则检查
        patient_diagnosis_codes = set(patient_features.get("diagnosis_codes", []))
        for diagnosis_code, diag_rules in self._rule_applicability_index["diagnosis_dependent"].items():
            if rule_id in diag_rules and diagnosis_code not in patient_diagnosis_codes:
                filter_reasons["diagnosis_missing"] += 1
                return False

        # 4. 药品特定规则检查
        patient_medication_codes = set(patient_features.get("medication_codes", []))
        for med_code, med_rules in self._rule_applicability_index["medication_specific"].items():
            if rule_id in med_rules and med_code not in patient_medication_codes:
                filter_reasons["medication_missing"] += 1
                return False

        # 5. 费用相关规则检查
        if not patient_features["has_fees"]:
            rule_instance = RULE_CACHE.get(rule_id)
            if rule_instance and self._is_fee_dependent_rule(rule_instance):
                filter_reasons["no_fees"] += 1
                return False

        # 规则适用
        filter_reasons["applicable"] += 1
        return True

    def _is_fee_dependent_rule(self, rule_instance) -> bool:
        """判断规则是否依赖费用数据"""
        rule_name = rule_instance.__class__.__name__.lower()
        fee_dependent_patterns = ["cost", "fee", "amount", "price", "dosage", "frequency", "quantity", "days", "total"]
        return any(pattern in rule_name for pattern in fee_dependent_patterns)

    def get_filter_statistics(self) -> dict[str, Any]:
        """获取筛选统计信息"""
        return {
            "total_indexed_rules": len(RULE_CACHE),
            "index_sizes": {
                "gender_specific": sum(
                    len(rules) for rules in self._rule_applicability_index["gender_specific"].values()
                ),
                "age_specific": sum(len(rules) for rules in self._rule_applicability_index["age_specific"].values()),
                "diagnosis_dependent": sum(
                    len(rules) for rules in self._rule_applicability_index["diagnosis_dependent"].values()
                ),
                "medication_specific": sum(
                    len(rules) for rules in self._rule_applicability_index["medication_specific"].values()
                ),
            },
            "filter_stats": dict(self.filter_stats),
        }


# 全局规则筛选器实例
rule_filter = RuleApplicabilityFilter()
