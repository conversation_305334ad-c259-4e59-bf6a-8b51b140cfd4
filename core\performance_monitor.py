"""
性能监控模块
用于监控系统资源使用情况，为动态进程池管理提供数据支持
"""

import os
import threading
import time
from collections import deque
from dataclasses import dataclass

try:
    import psutil

    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

from config.settings import settings
from core.logging.logging_system import log as logger


@dataclass
class SystemMetrics:
    """系统性能指标"""

    cpu_usage: float  # CPU使用率 (0-100)
    memory_usage: float  # 内存使用率 (0-100)
    memory_available: int  # 可用内存 (MB)
    process_count: int  # 当前进程数
    queue_length: int  # 任务队列长度
    timestamp: float  # 时间戳

    # 数据库连接池指标
    db_pool_size: int = 0  # 连接池大小
    db_active_connections: int = 0  # 活跃连接数
    db_idle_connections: int = 0  # 空闲连接数
    db_overflow_connections: int = 0  # 溢出连接数
    db_pool_utilization: float = 0.0  # 连接池使用率


@dataclass
class RegistrationTaskMetrics:
    """注册任务性能指标"""

    task_id: str  # 任务ID
    batch_size: int  # 批次大小
    processing_time: float  # 处理时间（秒）
    success: bool  # 是否成功
    memory_usage_mb: float  # 内存使用量（MB）
    timestamp: float  # 时间戳
    error_message: str = ""  # 错误信息（如果失败）


@dataclass
class RegistrationPerformanceStats:
    """注册任务性能统计信息"""

    total_tasks: int  # 总任务数
    successful_tasks: int  # 成功任务数
    failed_tasks: int  # 失败任务数
    success_rate: float  # 成功率
    avg_processing_time: float  # 平均处理时间
    max_processing_time: float  # 最大处理时间
    min_processing_time: float  # 最小处理时间
    avg_batch_size: float  # 平均批次大小
    max_batch_size: int  # 最大批次大小
    min_batch_size: int  # 最小批次大小
    avg_memory_usage: float  # 平均内存使用量
    max_memory_usage: float  # 最大内存使用量
    total_processing_time: float  # 总处理时间
    throughput: float  # 吞吐量（任务/秒）
    sample_count: int  # 样本数量


@dataclass
class PerformanceStats:
    """性能统计信息"""

    avg_cpu_usage: float
    max_cpu_usage: float
    avg_memory_usage: float
    max_memory_usage: float
    avg_queue_length: float
    max_queue_length: int
    sample_count: int

    # 数据库连接池统计
    avg_db_pool_utilization: float = 0.0
    max_db_active_connections: int = 0
    avg_db_active_connections: float = 0.0


class PerformanceMonitor:
    """
    性能监控器
    实时监控系统资源使用情况，为动态调整提供数据支持
    """

    def __init__(self, history_size: int = 60, sample_interval: float = 1.0):
        """
        初始化性能监控器

        Args:
            history_size: 保留的历史数据点数量
            sample_interval: 采样间隔（秒）
        """
        if not PSUTIL_AVAILABLE:
            logger.warning("psutil not available, performance monitoring will be limited")

        self.history_size = history_size
        self.sample_interval = sample_interval
        self.metrics_history = deque(maxlen=history_size)
        self.current_queue_length = 0
        self.is_monitoring = False
        self.monitor_thread: threading.Thread | None = None
        self._lock = threading.Lock()

        # 注册任务性能监控相关属性
        self.registration_monitoring_enabled = getattr(
            settings, "RULE_REGISTRATION_PERFORMANCE_MONITORING_ENABLED", True
        )
        if self.registration_monitoring_enabled:
            registration_history_size = getattr(settings, "RULE_REGISTRATION_METRICS_HISTORY_SIZE", 100)
            self.registration_metrics_history = deque(maxlen=registration_history_size)
            self._registration_lock = threading.Lock()
            logger.debug("Registration task performance monitoring enabled")
        else:
            self.registration_metrics_history = None
            self._registration_lock = None
            logger.debug("Registration task performance monitoring disabled")

        # 获取系统信息
        self.cpu_count = os.cpu_count() or 4
        if PSUTIL_AVAILABLE:
            self.total_memory = psutil.virtual_memory().total // (1024 * 1024)  # MB
        else:
            self.total_memory = 8192  # 默认8GB

        logger.debug(f"PerformanceMonitor initialized: CPU cores={self.cpu_count}, Total memory={self.total_memory}MB")

    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            logger.warning("Performance monitoring is already running")
            return

        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("Performance monitoring started")

    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return

        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        logger.info("Performance monitoring stopped")

    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                metrics = self._collect_metrics()
                with self._lock:
                    self.metrics_history.append(metrics)

                # 每10次采样记录一次日志
                if len(self.metrics_history) % 10 == 0:
                    logger.trace(
                        f"System metrics: CPU={metrics.cpu_usage:.1f}%, "
                        f"Memory={metrics.memory_usage:.1f}%, "
                        f"Queue={metrics.queue_length}",
                    )

                time.sleep(self.sample_interval)
            except Exception as e:
                logger.error(f"Error in performance monitoring: {e}")
                time.sleep(self.sample_interval)

    def _collect_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        if PSUTIL_AVAILABLE:
            # CPU使用率
            cpu_usage = psutil.cpu_percent(interval=0.1)

            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            memory_available = memory.available // (1024 * 1024)  # MB

            # 进程数量
            process_count = len(psutil.pids())
        else:
            # 使用默认值
            cpu_usage = 50.0  # 假设50%使用率
            memory_usage = 60.0  # 假设60%内存使用
            memory_available = int(self.total_memory * 0.4)  # 假设40%可用
            process_count = 100  # 假设100个进程

        # 收集数据库连接池指标
        db_metrics = self._collect_db_metrics()

        return SystemMetrics(
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            memory_available=memory_available,
            process_count=process_count,
            queue_length=self.current_queue_length,
            timestamp=time.perf_counter(),
            **db_metrics,
        )

    def _collect_db_metrics(self) -> dict:
        """收集数据库连接池指标"""
        try:
            # 导入连接池管理器
            from core.db_pool_manager import get_pool_manager

            pool_manager = get_pool_manager()
            if pool_manager:
                pool_status = pool_manager.get_pool_status()
                return {
                    "db_pool_size": pool_status.get("pool_size", 0),
                    "db_active_connections": pool_status.get("checked_out", 0),
                    "db_idle_connections": pool_status.get("checked_in", 0),
                    "db_overflow_connections": pool_status.get("overflow", 0),
                    "db_pool_utilization": pool_status.get("utilization_rate", 0.0),
                }
            else:
                return {
                    "db_pool_size": 0,
                    "db_active_connections": 0,
                    "db_idle_connections": 0,
                    "db_overflow_connections": 0,
                    "db_pool_utilization": 0.0,
                }
        except Exception as e:
            logger.error(f"Error collecting database metrics: {e}")
            return {
                "db_pool_size": 0,
                "db_active_connections": 0,
                "db_idle_connections": 0,
                "db_overflow_connections": 0,
                "db_pool_utilization": 0.0,
            }

    def update_queue_length(self, length: int):
        """更新任务队列长度"""
        self.current_queue_length = length

    def record_registration_task_performance(
        self, task_id: str, batch_size: int, processing_time: float, success: bool, error_message: str = ""
    ):
        """
        记录注册任务性能指标

        Args:
            task_id: 任务ID
            batch_size: 批次大小
            processing_time: 处理时间（秒）
            success: 是否成功
            error_message: 错误信息（如果失败）
        """
        if not self.registration_monitoring_enabled or not self.registration_metrics_history:
            return

        # 获取当前内存使用量
        memory_usage_mb = 0.0
        if PSUTIL_AVAILABLE:
            try:
                process = psutil.Process()
                memory_usage_mb = process.memory_info().rss / (1024 * 1024)  # MB
            except Exception as e:
                logger.debug(f"Failed to get memory usage: {e}")

        # 创建性能指标记录
        metrics = RegistrationTaskMetrics(
            task_id=task_id,
            batch_size=batch_size,
            processing_time=processing_time,
            success=success,
            memory_usage_mb=memory_usage_mb,
            timestamp=time.perf_counter(),
            error_message=error_message,
        )

        # 线程安全地添加到历史记录
        with self._registration_lock:
            self.registration_metrics_history.append(metrics)

        # 记录日志
        if success:
            logger.debug(
                f"Registration task performance recorded: task_id={task_id}, "
                f"batch_size={batch_size}, processing_time={processing_time:.3f}s, "
                f"memory_usage={memory_usage_mb:.1f}MB"
            )
        else:
            logger.warning(
                f"Registration task failed: task_id={task_id}, "
                f"batch_size={batch_size}, processing_time={processing_time:.3f}s, "
                f"error={error_message}"
            )

    def get_current_metrics(self) -> SystemMetrics | None:
        """获取当前指标"""
        with self._lock:
            if self.metrics_history:
                return self.metrics_history[-1]
        return None

    def get_registration_performance_stats(self, window_size: int | None = None) -> RegistrationPerformanceStats | None:
        """
        获取注册任务性能统计信息

        Args:
            window_size: 统计窗口大小，None表示使用所有历史数据

        Returns:
            注册任务性能统计信息
        """
        if not self.registration_monitoring_enabled or not self.registration_metrics_history:
            return None

        with self._registration_lock:
            if not self.registration_metrics_history:
                return None

            # 确定统计窗口
            if window_size is None:
                data = list(self.registration_metrics_history)
            else:
                data = list(self.registration_metrics_history)[-window_size:]

            if not data:
                return None

            # 计算统计信息
            total_tasks = len(data)
            successful_tasks = sum(1 for m in data if m.success)
            failed_tasks = total_tasks - successful_tasks
            success_rate = successful_tasks / total_tasks if total_tasks > 0 else 0.0

            processing_times = [m.processing_time for m in data]
            batch_sizes = [m.batch_size for m in data]
            memory_usages = [m.memory_usage_mb for m in data]

            avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0.0
            max_processing_time = max(processing_times) if processing_times else 0.0
            min_processing_time = min(processing_times) if processing_times else 0.0

            avg_batch_size = sum(batch_sizes) / len(batch_sizes) if batch_sizes else 0.0
            max_batch_size = max(batch_sizes) if batch_sizes else 0
            min_batch_size = min(batch_sizes) if batch_sizes else 0

            avg_memory_usage = sum(memory_usages) / len(memory_usages) if memory_usages else 0.0
            max_memory_usage = max(memory_usages) if memory_usages else 0.0

            total_processing_time = sum(processing_times)

            # 计算吞吐量（任务/秒）
            if data and len(data) > 1:
                time_span = data[-1].timestamp - data[0].timestamp
                throughput = total_tasks / time_span if time_span > 0 else 0.0
            else:
                throughput = 0.0

            return RegistrationPerformanceStats(
                total_tasks=total_tasks,
                successful_tasks=successful_tasks,
                failed_tasks=failed_tasks,
                success_rate=success_rate,
                avg_processing_time=avg_processing_time,
                max_processing_time=max_processing_time,
                min_processing_time=min_processing_time,
                avg_batch_size=avg_batch_size,
                max_batch_size=max_batch_size,
                min_batch_size=min_batch_size,
                avg_memory_usage=avg_memory_usage,
                max_memory_usage=max_memory_usage,
                total_processing_time=total_processing_time,
                throughput=throughput,
                sample_count=total_tasks,
            )

    def get_performance_stats(self, window_size: int | None = None) -> PerformanceStats | None:
        """
        获取性能统计信息

        Args:
            window_size: 统计窗口大小，None表示使用所有历史数据

        Returns:
            性能统计信息
        """
        with self._lock:
            if not self.metrics_history:
                return None

            # 确定统计窗口
            if window_size is None:
                data = list(self.metrics_history)
            else:
                data = list(self.metrics_history)[-window_size:]

            if not data:
                return None

            # 计算统计信息
            cpu_values = [m.cpu_usage for m in data]
            memory_values = [m.memory_usage for m in data]
            queue_values = [m.queue_length for m in data]

            # 数据库连接池统计
            db_utilization_values = [m.db_pool_utilization for m in data]
            db_active_values = [m.db_active_connections for m in data]

            return PerformanceStats(
                avg_cpu_usage=sum(cpu_values) / len(cpu_values),
                max_cpu_usage=max(cpu_values),
                avg_memory_usage=sum(memory_values) / len(memory_values),
                max_memory_usage=max(memory_values),
                avg_queue_length=sum(queue_values) / len(queue_values),
                max_queue_length=max(queue_values),
                sample_count=len(data),
                avg_db_pool_utilization=sum(db_utilization_values) / len(db_utilization_values),
                max_db_active_connections=max(db_active_values) if db_active_values else 0,
                avg_db_active_connections=sum(db_active_values) / len(db_active_values) if db_active_values else 0.0,
            )

    def should_scale_up(self, cpu_threshold: float = 70.0, queue_threshold: int = 5) -> bool:
        """
        判断是否应该扩容

        Args:
            cpu_threshold: CPU使用率阈值
            queue_threshold: 队列长度阈值

        Returns:
            是否应该扩容
        """
        stats = self.get_performance_stats(window_size=10)  # 最近10个采样点
        if not stats:
            return False

        # CPU使用率低且队列较长，说明需要更多进程
        return stats.avg_cpu_usage < cpu_threshold and stats.avg_queue_length > queue_threshold

    def should_scale_down(self, cpu_threshold: float = 90.0, queue_threshold: int = 2) -> bool:
        """
        判断是否应该缩容

        Args:
            cpu_threshold: CPU使用率阈值
            queue_threshold: 队列长度阈值

        Returns:
            是否应该缩容
        """
        stats = self.get_performance_stats(window_size=10)  # 最近10个采样点
        if not stats:
            return False

        # CPU使用率高或队列很短，说明进程过多
        return stats.avg_cpu_usage > cpu_threshold or stats.avg_queue_length < queue_threshold

    def get_optimal_worker_count(self, min_workers: int = 2, max_workers: int = 16) -> int:
        """
        根据当前系统状态计算最优工作进程数

        Args:
            min_workers: 最小工作进程数
            max_workers: 最大工作进程数

        Returns:
            建议的工作进程数
        """
        stats = self.get_performance_stats(window_size=20)  # 最近20个采样点
        if not stats:
            return min_workers

        # 基于CPU使用率和队列长度的启发式算法
        if stats.avg_cpu_usage < 50 and stats.avg_queue_length > 10:
            # CPU空闲但队列较长，增加进程
            suggested = min(max_workers, self.cpu_count * 2)
        elif stats.avg_cpu_usage > 85:
            # CPU使用率过高，减少进程
            suggested = max(min_workers, self.cpu_count)
        elif stats.avg_queue_length < 2:
            # 队列很短，可以减少进程
            suggested = max(min_workers, self.cpu_count)
        else:
            # 正常情况，使用CPU核心数的1.5倍
            suggested = max(min_workers, min(max_workers, int(self.cpu_count * 1.5)))

        return suggested

    def get_registration_optimization_recommendations(self) -> dict[str, any]:
        """
        基于注册任务性能数据提供优化建议

        Returns:
            优化建议字典
        """
        if not self.registration_monitoring_enabled:
            return {"enabled": False, "message": "Registration performance monitoring is disabled"}

        stats = self.get_registration_performance_stats(window_size=50)  # 最近50个任务
        if not stats:
            return {"enabled": True, "message": "No registration task data available"}

        recommendations = {
            "enabled": True,
            "stats_summary": {
                "total_tasks": stats.total_tasks,
                "success_rate": stats.success_rate,
                "avg_processing_time": stats.avg_processing_time,
                "avg_batch_size": stats.avg_batch_size,
                "throughput": stats.throughput,
            },
            "recommendations": [],
        }

        # 获取配置阈值
        cpu_threshold = getattr(settings, "RULE_REGISTRATION_PERFORMANCE_CPU_THRESHOLD", 80.0)
        memory_threshold = getattr(settings, "RULE_REGISTRATION_PERFORMANCE_MEMORY_THRESHOLD", 85.0)
        success_rate_threshold = getattr(settings, "RULE_REGISTRATION_PERFORMANCE_SUCCESS_RATE_THRESHOLD", 0.95)
        avg_time_threshold = getattr(settings, "RULE_REGISTRATION_PERFORMANCE_AVG_TIME_THRESHOLD", 5.0)
        min_batch_size = getattr(settings, "RULE_REGISTRATION_BATCH_SIZE_MIN", 50)
        max_batch_size = getattr(settings, "RULE_REGISTRATION_BATCH_SIZE_MAX", 500)

        # 分析成功率
        if stats.success_rate < success_rate_threshold:
            recommendations["recommendations"].append(
                {
                    "type": "success_rate",
                    "priority": "high",
                    "message": f"成功率过低 ({stats.success_rate:.2%})，建议检查错误原因并优化重试策略",
                    "current_value": stats.success_rate,
                    "threshold": success_rate_threshold,
                }
            )

        # 分析处理时间
        if stats.avg_processing_time > avg_time_threshold:
            recommendations["recommendations"].append(
                {
                    "type": "processing_time",
                    "priority": "medium",
                    "message": f"平均处理时间过长 ({stats.avg_processing_time:.2f}s)，建议减小批次大小或优化处理逻辑",
                    "current_value": stats.avg_processing_time,
                    "threshold": avg_time_threshold,
                }
            )

        # 分析批次大小
        if stats.avg_batch_size < min_batch_size:
            recommendations["recommendations"].append(
                {
                    "type": "batch_size",
                    "priority": "low",
                    "message": f"批次大小过小 ({stats.avg_batch_size:.0f})，可以适当增大以提高吞吐量",
                    "current_value": stats.avg_batch_size,
                    "suggested_value": min(stats.avg_batch_size * 1.5, max_batch_size),
                }
            )
        elif stats.avg_batch_size > max_batch_size:
            recommendations["recommendations"].append(
                {
                    "type": "batch_size",
                    "priority": "medium",
                    "message": f"批次大小过大 ({stats.avg_batch_size:.0f})，可能导致内存压力，建议减小",
                    "current_value": stats.avg_batch_size,
                    "suggested_value": max(stats.avg_batch_size * 0.8, min_batch_size),
                }
            )

        # 分析内存使用
        if stats.max_memory_usage > memory_threshold:
            recommendations["recommendations"].append(
                {
                    "type": "memory_usage",
                    "priority": "high",
                    "message": f"内存使用过高 ({stats.max_memory_usage:.1f}MB)，建议减小批次大小或优化内存使用",
                    "current_value": stats.max_memory_usage,
                    "threshold": memory_threshold,
                }
            )

        # 分析吞吐量
        if stats.throughput > 0:
            # 获取系统指标进行综合分析
            system_stats = self.get_performance_stats(window_size=10)
            if system_stats:
                if system_stats.avg_cpu_usage < 50 and stats.throughput < 1.0:
                    recommendations["recommendations"].append(
                        {
                            "type": "throughput",
                            "priority": "medium",
                            "message": f"CPU使用率较低但吞吐量不高 ({stats.throughput:.2f} tasks/s)，建议增大批次大小",
                            "current_value": stats.throughput,
                            "cpu_usage": system_stats.avg_cpu_usage,
                        }
                    )

        # 如果没有建议，添加正常状态信息
        if not recommendations["recommendations"]:
            recommendations["recommendations"].append(
                {"type": "status", "priority": "info", "message": "注册任务性能表现良好，无需特殊优化"}
            )

        return recommendations


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()
