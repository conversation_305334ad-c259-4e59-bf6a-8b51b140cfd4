"""
统一日志配置模块

提供简化的统一日志配置，支持所有环境的日志需求。
移除了复杂的环境特定配置类，使用简单的环境变量控制。

作者: 系统架构师
版本: 2.0.0 (简化重构版)
"""

from typing import Any

from .settings import settings


class UnifiedLogConfig:
    """
    统一日志配置类

    简化的日志配置系统，通过环境变量控制所有环境的日志行为。
    移除了复杂的环境特定配置类和工厂模式。
    """

    def __init__(self):
        """初始化统一日志配置"""
        pass

    def get_log_level(self) -> str:
        """获取日志级别"""
        return settings.LOG_LEVEL.upper()

    def get_log_format(self) -> str:
        """获取日志格式"""
        # 直接返回设置中的日志格式，不添加环境标识
        return settings.LOG_FORMAT

    def get_stdout_config(self) -> dict[str, Any]:
        """获取控制台输出配置"""
        # 为控制台使用简化的彩色格式，不包含环境标识
        console_format = "<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"

        return {
            "enabled": settings.LOG_STDOUT_ENABLED,
            "level": self.get_log_level(),
            "format": console_format,
        }

    def get_file_config(self) -> dict[str, Any]:
        """获取文件输出配置"""
        return {
            "path": settings.LOG_PATH,
            "rotation": settings.LOG_ROTATION,
            "retention": settings.LOG_RETENTION,
            "compression": settings.LOG_COMPRESSION,
        }

    def build_config(self) -> dict[str, Any]:
        """
        构建完整的日志配置

        Returns:
            dict: 完整的日志配置字典
        """
        return {
            "level": self.get_log_level(),
            "format": self.get_log_format(),
            "path": settings.LOG_PATH,
            "rotation": settings.LOG_ROTATION,
            "retention": settings.LOG_RETENTION,
            "compression": settings.LOG_COMPRESSION,
            "enqueue": settings.LOG_ENQUEUE,
            "stdout_sink": self.get_stdout_config(),
            "file_sink": self.get_file_config(),
        }


# 创建全局配置实例
_unified_config = UnifiedLogConfig()


def get_log_config() -> UnifiedLogConfig:
    """
    获取统一日志配置实例

    Returns:
        UnifiedLogConfig: 统一日志配置实例
    """
    return _unified_config


# 向后兼容的配置字典（用于旧代码）
LOGGING_CONFIG = _unified_config.build_config()
