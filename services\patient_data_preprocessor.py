"""
patients数据预处理器
实现极致的数据预处理和内存优化，支持亚秒级规则校验
"""

import hashlib
import time
from collections import defaultdict
from dataclasses import dataclass
from typing import Any

from core.logging.logging_system import log as logger
from models.optimized_patient import OptimizedFeeItem, OptimizedPatientData
from models.patient import PatientData


@dataclass
class PreprocessingStats:
    """预处理统计信息"""

    original_fees_count: int
    optimized_fees_count: int
    preprocessing_time_ms: float
    memory_saved_bytes: int
    index_build_time_ms: float


class UltraFastFeeIndex:
    """
    超快速费用索引
    使用多级哈希表和预计算，实现O(1)查找性能
    """

    __slots__ = [
        "_by_ybdm",
        "_by_date",
        "_by_id",
        "_all_items",
        "_ybdm_totals",
        "_date_totals",
        "_category_totals",
        "_quick_stats",
        "_memory_usage",
    ]

    def __init__(self):
        # 基础索引（继承原有功能）
        self._by_ybdm: dict[str, list[OptimizedFeeItem]] = {}
        self._by_date: dict[str, list[OptimizedFeeItem]] = {}
        self._by_id: dict[str, OptimizedFeeItem] = {}
        self._all_items: list[OptimizedFeeItem] = []

        # 预计算的统计数据（避免运行时计算）
        self._ybdm_totals: dict[str, tuple[float, float, int]] = {}  # (总金额, 总数量, 总天数)
        self._date_totals: dict[str, tuple[float, float, int]] = {}  # (总金额, 总数量, 项目数)
        self._category_totals: dict[str, tuple[float, float, int]] = {}  # 按费用类别统计

        # 快速统计信息
        self._quick_stats = {
            "total_amount": 0.0,
            "total_quantity": 0.0,
            "unique_dates": 0,
            "unique_ybdm": 0,
            "total_items": 0,
        }

        self._memory_usage = 0

    def build_from_fees(self, fees: list[OptimizedFeeItem]) -> float:
        """
        从费用列表构建索引，返回构建时间(ms)
        """
        start_time = time.perf_counter()

        # 清空现有索引
        self._clear_all()

        # 构建基础索引
        for fee in fees:
            self._add_item_to_index(fee)

        # 预计算统计数据
        self._precompute_statistics()

        build_time = (time.perf_counter() - start_time) * 1000
        logger.debug(f"超快速索引构建完成，{len(fees)} 项费用，耗时 {build_time:.2f}ms")
        return build_time

    def _add_item_to_index(self, item: OptimizedFeeItem):
        """添加项目到索引"""
        self._all_items.append(item)

        # YBDM索引
        if item.ybdm:
            if item.ybdm not in self._by_ybdm:
                self._by_ybdm[item.ybdm] = []
            self._by_ybdm[item.ybdm].append(item)

        # 日期索引
        date = item.get_date()
        if date:
            if date not in self._by_date:
                self._by_date[date] = []
            self._by_date[date].append(item)

        # ID索引
        if item.id:
            self._by_id[item.id] = item

    def _precompute_statistics(self):
        """预计算所有统计数据"""
        # 按YBDM预计算
        ybdm_stats = defaultdict(lambda: [0.0, 0.0, set()])  # [总金额, 总数量, 日期集合]

        for item in self._all_items:
            if item.ybdm:
                stats = ybdm_stats[item.ybdm]
                stats[0] += item.je  # 总金额
                stats[1] += item.sl  # 总数量
                if item.get_date():
                    stats[2].add(item.get_date())  # 日期集合

        # 转换为最终格式
        for ybdm, stats in ybdm_stats.items():
            self._ybdm_totals[ybdm] = (stats[0], stats[1], len(stats[2]))

        # 按日期预计算
        date_stats = defaultdict(lambda: [0.0, 0.0, 0])  # [总金额, 总数量, 项目数]

        for item in self._all_items:
            date = item.get_date()
            if date:
                stats = date_stats[date]
                stats[0] += item.je
                stats[1] += item.sl
                stats[2] += 1

        for date, stats in date_stats.items():
            self._date_totals[date] = (stats[0], stats[1], stats[2])

        # 全局快速统计
        self._quick_stats.update(
            {
                "total_amount": sum(item.je for item in self._all_items),
                "total_quantity": sum(item.sl for item in self._all_items),
                "unique_dates": len(self._by_date),
                "unique_ybdm": len(self._by_ybdm),
                "total_items": len(self._all_items),
            }
        )

    def get_by_ybdm_fast(self, ybdm: str) -> list[OptimizedFeeItem]:
        """O(1)查找，基于预构建索引"""
        return self._by_ybdm.get(ybdm, [])

    def get_ybdm_totals_fast(self, ybdm: str) -> tuple[float, float, int]:
        """O(1)获取YBDM统计，无需重新计算"""
        return self._ybdm_totals.get(ybdm, (0.0, 0.0, 0))

    def get_ybdm_list_totals_fast(self, ybdm_list: list[str]) -> tuple[float, float, int]:
        """O(n)获取多个YBDM的汇总统计"""
        total_amount = 0.0
        total_quantity = 0.0
        all_dates = set()

        for ybdm in ybdm_list:
            amount, quantity, _ = self._ybdm_totals.get(ybdm, (0.0, 0.0, 0))
            total_amount += amount
            total_quantity += quantity

            # 收集日期（需要去重）
            for item in self._by_ybdm.get(ybdm, []):
                date = item.get_date()
                if date:
                    all_dates.add(date)

        return total_amount, total_quantity, len(all_dates)

    def get_quick_stats(self) -> dict[str, Any]:
        """O(1)获取预计算的统计信息"""
        return self._quick_stats.copy()

    def _clear_all(self):
        """清空所有索引"""
        self._by_ybdm.clear()
        self._by_date.clear()
        self._by_id.clear()
        self._all_items.clear()
        self._ybdm_totals.clear()
        self._date_totals.clear()
        self._category_totals.clear()
        self._quick_stats = {
            "total_amount": 0.0,
            "total_quantity": 0.0,
            "unique_dates": 0,
            "unique_ybdm": 0,
            "total_items": 0,
        }

    def estimate_memory_usage(self) -> int:
        """估算内存使用量（字节）"""
        base_size = 500  # 基础对象

        # 费用项内存
        fees_memory = len(self._all_items) * 300

        # 索引内存
        index_memory = len(self._by_ybdm) * 100 + len(self._by_date) * 100 + len(self._by_id) * 50

        # 预计算统计内存
        stats_memory = len(self._ybdm_totals) * 50 + len(self._date_totals) * 50

        return base_size + fees_memory + index_memory + stats_memory


class UltraOptimizedPatientData:
    """
    超优化患者数据
    在 OptimizedPatientData 基础上进一步优化，专为极速校验设计
    """

    __slots__ = [
        "bah",
        "patient_hash",
        "basic_info_hash",
        "ultra_fee_index",
        "diagnosis_codes_set",
        "diagnosis_hash",
        "quick_features",
        "_memory_estimate",
    ]

    def __init__(self, optimized_data: OptimizedPatientData):
        """从优化数据创建超优化版本"""
        self.bah = optimized_data.bah

        # 预计算患者哈希（用于缓存）
        self.patient_hash = self._compute_patient_hash(optimized_data)
        self.basic_info_hash = self._compute_basic_info_hash(optimized_data.basic_information)

        # 使用超快速费用索引
        self.ultra_fee_index = UltraFastFeeIndex()
        fee_items = optimized_data.get_all_fees()
        self.ultra_fee_index.build_from_fees(fee_items)

        # 诊断编码集合（快速查找）
        self.diagnosis_codes_set = set(optimized_data.get_diagnosis_codes())
        self.diagnosis_hash = hash(frozenset(self.diagnosis_codes_set))

        # 预计算常用特征
        self.quick_features = self._precompute_features(optimized_data)

        # 内存使用估算
        self._memory_estimate = self._estimate_memory_usage()

    def _compute_patient_hash(self, data: OptimizedPatientData) -> str:
        """计算患者数据哈希"""
        hash_data = f"{data.bah}_{len(data.get_all_fees())}_{len(data.diagnoses)}"
        if data.basic_information:
            hash_data += f"_{data.basic_information.gender}_{data.basic_information.age}"
        return hashlib.md5(hash_data.encode()).hexdigest()[:16]

    def _compute_basic_info_hash(self, basic_info) -> str:
        """计算基本信息哈希"""
        if not basic_info:
            return "empty"
        hash_data = f"{basic_info.gender}_{basic_info.age}_{basic_info.birth_date}"
        return hashlib.md5(hash_data.encode()).hexdigest()[:8]

    def _precompute_features(self, data: OptimizedPatientData) -> dict[str, Any]:
        """预计算常用特征"""
        basic_info = data.basic_information

        features = {
            # 基本信息
            "gender": basic_info.gender if basic_info else None,
            "age": basic_info.age if basic_info else None,
            "age_group": self._get_age_group(basic_info.age if basic_info else None),
            # 费用相关
            "has_fees": len(data.get_all_fees()) > 0,
            "fee_count": len(data.get_all_fees()),
            "unique_ybdm_count": len(data.fee_index.get_unique_ybdm()),
            # 诊断相关
            "has_diagnoses": len(self.diagnosis_codes_set) > 0,
            "diagnosis_count": len(self.diagnosis_codes_set),
            # 时间相关
            "unique_dates": len(data.fee_index.get_unique_dates()),
        }

        return features

    def _get_age_group(self, age: int | None) -> str:
        """获取年龄组"""
        if age is None:
            return "unknown"
        elif age < 18:
            return "pediatric"
        elif age > 65:
            return "elderly"
        else:
            return "adult"

    def get_fees_by_codes_ultra_fast(self, ybdm_codes: list[str]) -> list[OptimizedFeeItem]:
        """超快速获取费用项"""
        result = []
        for code in ybdm_codes:
            result.extend(self.ultra_fee_index.get_by_ybdm_fast(code))
        return result

    def calculate_totals_ultra_fast(self, ybdm_codes: list[str] | None = None) -> tuple[float, float, int]:
        """超快速计算总计（使用预计算结果）"""
        if ybdm_codes:
            return self.ultra_fee_index.get_ybdm_list_totals_fast(ybdm_codes)
        else:
            stats = self.ultra_fee_index.get_quick_stats()
            return stats["total_amount"], stats["total_quantity"], stats["unique_dates"]

    def has_diagnosis_ultra_fast(self, code: str) -> bool:
        """超快速诊断检查"""
        return code in self.diagnosis_codes_set

    def has_any_diagnosis_ultra_fast(self, codes: list[str]) -> bool:
        """超快速多诊断检查"""
        return bool(self.diagnosis_codes_set.intersection(codes))

    def get_feature_fast(self, feature_name: str) -> Any:
        """快速获取预计算特征"""
        return self.quick_features.get(feature_name)

    def _estimate_memory_usage(self) -> int:
        """估算内存使用"""
        base_size = 200
        index_size = self.ultra_fee_index.estimate_memory_usage()
        diagnosis_size = len(self.diagnosis_codes_set) * 50
        features_size = len(self.quick_features) * 20

        return base_size + index_size + diagnosis_size + features_size

    def get_memory_usage_estimate(self) -> int:
        """获取内存使用估算"""
        return self._memory_estimate


class PatientDataPreprocessor:
    """
    患者数据预处理器
    实现极致的数据预处理和优化策略
    """

    def __init__(self):
        """初始化预处理器"""
        self.preprocessing_stats = defaultdict(int)
        self.total_memory_saved = 0
        self.total_preprocessing_time = 0.0

        logger.debug("患者数据预处理器初始化完成")

    def preprocess_patient_data(
        self, patient_data: PatientData
    ) -> tuple[UltraOptimizedPatientData, PreprocessingStats]:
        """
        预处理患者数据，返回超优化版本

        Args:
            patient_data: 原始患者数据

        Returns:
            tuple: (超优化患者数据, 预处理统计)
        """
        start_time = time.perf_counter()

        # 第一步：转换为基础优化数据
        optimized_data = OptimizedPatientData(patient_data)

        # 第二步：进一步优化为超优化数据
        index_build_start = time.perf_counter()
        ultra_optimized = UltraOptimizedPatientData(optimized_data)
        index_build_time = (time.perf_counter() - index_build_start) * 1000

        # 计算统计信息
        original_fees = len(patient_data.fees) if patient_data.fees else 0
        optimized_fees = len(ultra_optimized.ultra_fee_index._all_items)

        preprocessing_time = (time.perf_counter() - start_time) * 1000

        # 估算内存节省
        original_memory = self._estimate_original_memory(patient_data)
        optimized_memory = ultra_optimized.get_memory_usage_estimate()
        memory_saved = original_memory - optimized_memory

        stats = PreprocessingStats(
            original_fees_count=original_fees,
            optimized_fees_count=optimized_fees,
            preprocessing_time_ms=preprocessing_time,
            memory_saved_bytes=memory_saved,
            index_build_time_ms=index_build_time,
        )

        self.preprocessing_stats["total_processed"] += 1
        self.total_memory_saved += memory_saved
        self.total_preprocessing_time += preprocessing_time

        logger.debug(
            f"患者数据预处理完成: {patient_data.bah}, "
            f"费用 {original_fees} -> {optimized_fees}, "
            f"内存节省 {memory_saved / 1024:.1f}KB, "
            f"耗时 {preprocessing_time:.2f}ms"
        )

        return ultra_optimized, stats

    def _estimate_original_memory(self, patient_data: PatientData) -> int:
        """估算原始数据内存使用"""
        base_size = 1000  # 基础结构

        # 费用数据
        fee_count = len(patient_data.fees) if patient_data.fees else 0
        fees_memory = fee_count * 800  # 每个原始费用项约800字节

        # 诊断数据
        diag_count = 0
        if patient_data.Diagnosis and patient_data.Diagnosis.diagnosis:
            diag_count = len(patient_data.Diagnosis.diagnosis)
        diagnosis_memory = diag_count * 200

        return base_size + fees_memory + diagnosis_memory

    def batch_preprocess(
        self, patient_data_list: list[PatientData]
    ) -> list[tuple[UltraOptimizedPatientData, PreprocessingStats]]:
        """批量预处理患者数据"""
        results = []
        start_time = time.perf_counter()

        for patient_data in patient_data_list:
            result = self.preprocess_patient_data(patient_data)
            results.append(result)

        total_time = (time.perf_counter() - start_time) * 1000

        logger.info(
            f"批量预处理完成: {len(patient_data_list)} 个患者, "
            f"总耗时 {total_time:.2f}ms, "
            f"平均 {total_time / len(patient_data_list):.2f}ms/患者"
        )

        return results

    def get_preprocessing_stats(self) -> dict[str, Any]:
        """获取预处理统计信息"""
        return {
            "total_processed": self.preprocessing_stats["total_processed"],
            "total_memory_saved_mb": self.total_memory_saved / (1024 * 1024),
            "total_preprocessing_time_ms": self.total_preprocessing_time,
            "avg_preprocessing_time_ms": (
                self.total_preprocessing_time / max(1, self.preprocessing_stats["total_processed"])
            ),
            "avg_memory_saved_kb": (
                self.total_memory_saved / (1024 * max(1, self.preprocessing_stats["total_processed"]))
            ),
        }


# 全局预处理器实例
patient_preprocessor = PatientDataPreprocessor()
