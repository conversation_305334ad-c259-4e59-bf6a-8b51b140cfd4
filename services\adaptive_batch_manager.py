"""
自适应批次大小管理器
基于系统性能指标动态调整批次大小，复用DynamicProcessPool的调整算法
"""

import threading
import time
from collections import deque
from dataclasses import dataclass

from config.settings import settings
from core.logging.logging_system import log as logger
from core.performance_monitor import performance_monitor


@dataclass
class BatchAdjustmentRecord:
    """批次大小调整记录"""

    timestamp: float  # 调整时间戳
    old_batch_size: int  # 调整前批次大小
    new_batch_size: int  # 调整后批次大小
    reason: str  # 调整原因
    cpu_usage: float  # 当时的CPU使用率
    memory_usage: float  # 当时的内存使用率
    success_rate: float  # 当时的成功率
    avg_processing_time: float  # 当时的平均处理时间


@dataclass
class BatchSizeStats:
    """批次大小统计信息"""

    current_batch_size: int  # 当前批次大小
    min_batch_size: int  # 最小批次大小
    max_batch_size: int  # 最大批次大小
    total_adjustments: int  # 总调整次数
    scale_up_count: int  # 扩大调整次数
    scale_down_count: int  # 缩小调整次数
    last_adjustment_time: float  # 最后调整时间
    avg_batch_size: float  # 平均批次大小
    adjustment_frequency: float  # 调整频率（次/小时）


class AdaptiveBatchSizeManager:
    """
    自适应批次大小管理器
    基于系统性能指标动态调整批次大小，复用DynamicProcessPool的调整逻辑
    """

    def __init__(
        self,
        initial_batch_size: int | None = None,
        min_batch_size: int | None = None,
        max_batch_size: int | None = None,
        adjustment_interval: float | None = None,
    ):
        """
        初始化自适应批次大小管理器

        Args:
            initial_batch_size: 初始批次大小，None表示使用配置值
            min_batch_size: 最小批次大小，None表示使用配置值
            max_batch_size: 最大批次大小，None表示使用配置值
            adjustment_interval: 调整间隔（秒），None表示使用配置值
        """
        # 从配置获取参数
        self.enabled = getattr(settings, "ADAPTIVE_BATCH_SIZE_ENABLED", True)
        self.min_batch_size = min_batch_size or getattr(settings, "RULE_REGISTRATION_BATCH_SIZE_MIN", 50)
        self.max_batch_size = max_batch_size or getattr(settings, "RULE_REGISTRATION_BATCH_SIZE_MAX", 500)
        self.adjustment_interval = adjustment_interval or getattr(settings, "ADAPTIVE_BATCH_ADJUSTMENT_INTERVAL", 30.0)

        # 调整阈值
        self.scale_up_cpu_threshold = getattr(settings, "ADAPTIVE_BATCH_SCALE_UP_CPU_THRESHOLD", 50.0)
        self.scale_down_cpu_threshold = getattr(settings, "ADAPTIVE_BATCH_SCALE_DOWN_CPU_THRESHOLD", 85.0)
        self.scale_up_memory_threshold = getattr(settings, "ADAPTIVE_BATCH_SCALE_UP_MEMORY_THRESHOLD", 70.0)
        self.scale_down_memory_threshold = getattr(settings, "ADAPTIVE_BATCH_SCALE_DOWN_MEMORY_THRESHOLD", 85.0)

        # 调整步长
        self.adjustment_step = getattr(settings, "ADAPTIVE_BATCH_ADJUSTMENT_STEP", 0.2)
        self.min_adjustment = getattr(settings, "ADAPTIVE_BATCH_MIN_ADJUSTMENT", 10)
        self.max_adjustment = getattr(settings, "ADAPTIVE_BATCH_MAX_ADJUSTMENT", 100)

        # 性能指标权重
        self.cpu_weight = getattr(settings, "ADAPTIVE_BATCH_CPU_WEIGHT", 0.4)
        self.memory_weight = getattr(settings, "ADAPTIVE_BATCH_MEMORY_WEIGHT", 0.3)
        self.success_rate_weight = getattr(settings, "ADAPTIVE_BATCH_SUCCESS_RATE_WEIGHT", 0.2)
        self.processing_time_weight = getattr(settings, "ADAPTIVE_BATCH_PROCESSING_TIME_WEIGHT", 0.1)

        # 当前批次大小
        self.current_batch_size = initial_batch_size or getattr(settings, "RULE_REGISTRATION_BATCH_SIZE", 100)
        self.current_batch_size = max(self.min_batch_size, min(self.max_batch_size, self.current_batch_size))

        # 调整历史记录
        history_size = getattr(settings, "ADAPTIVE_BATCH_ADJUSTMENT_HISTORY_SIZE", 50)
        self.adjustment_history = deque(maxlen=history_size)

        # 统计信息
        self.stats = BatchSizeStats(
            current_batch_size=self.current_batch_size,
            min_batch_size=self.min_batch_size,
            max_batch_size=self.max_batch_size,
            total_adjustments=0,
            scale_up_count=0,
            scale_down_count=0,
            last_adjustment_time=0.0,
            avg_batch_size=float(self.current_batch_size),
            adjustment_frequency=0.0,
        )

        # 线程安全锁
        self._lock = threading.Lock()

        logger.debug(
            f"AdaptiveBatchSizeManager initialized: "
            f"enabled={self.enabled}, "
            f"batch_size={self.current_batch_size} "
            f"(range: {self.min_batch_size}-{self.max_batch_size}), "
            f"adjustment_interval={self.adjustment_interval}s"
        )

    def get_current_batch_size(self) -> int:
        """获取当前批次大小"""
        with self._lock:
            return self.current_batch_size

    def calculate_optimal_batch_size(self) -> int:
        """
        基于当前系统性能指标计算最优批次大小
        复用DynamicProcessPool的调整逻辑

        Returns:
            建议的批次大小
        """
        if not self.enabled:
            return self.current_batch_size

        # 获取系统性能指标
        system_metrics = performance_monitor.get_current_metrics()
        if not system_metrics:
            logger.debug("No system metrics available for batch size calculation")
            return self.current_batch_size

        # 获取注册任务性能统计
        registration_stats = performance_monitor.get_registration_performance_stats(window_size=20)

        # 基于多因子算法计算最优批次大小
        return self._calculate_multi_factor_batch_size(system_metrics, registration_stats)

    def _calculate_multi_factor_batch_size(self, system_metrics, registration_stats) -> int:
        """
        基于多因子算法计算最优批次大小

        Args:
            system_metrics: 系统性能指标
            registration_stats: 注册任务性能统计

        Returns:
            建议的批次大小
        """
        current_size = self.current_batch_size
        suggested_size = current_size

        # 因子1：CPU使用率分析
        cpu_factor = self._analyze_cpu_factor(system_metrics.cpu_usage)

        # 因子2：内存使用率分析
        memory_factor = self._analyze_memory_factor(system_metrics.memory_usage)

        # 因子3：注册任务成功率分析
        success_rate_factor = 1.0
        if registration_stats:
            success_rate_factor = self._analyze_success_rate_factor(registration_stats.success_rate)

        # 因子4：处理时间分析
        processing_time_factor = 1.0
        if registration_stats:
            processing_time_factor = self._analyze_processing_time_factor(registration_stats.avg_processing_time)

        # 加权计算综合调整因子
        total_factor = (
            cpu_factor * self.cpu_weight
            + memory_factor * self.memory_weight
            + success_rate_factor * self.success_rate_weight
            + processing_time_factor * self.processing_time_weight
        )

        # 应用调整因子
        if total_factor > 1.1:  # 建议增大批次
            adjustment = max(self.min_adjustment, int(current_size * self.adjustment_step))
            adjustment = min(adjustment, self.max_adjustment)
            suggested_size = min(self.max_batch_size, current_size + adjustment)
        elif total_factor < 0.9:  # 建议减小批次
            adjustment = max(self.min_adjustment, int(current_size * self.adjustment_step))
            adjustment = min(adjustment, self.max_adjustment)
            suggested_size = max(self.min_batch_size, current_size - adjustment)

        logger.debug(
            f"Batch size calculation: current={current_size}, suggested={suggested_size}, "
            f"factors(cpu={cpu_factor:.2f}, mem={memory_factor:.2f}, "
            f"success={success_rate_factor:.2f}, time={processing_time_factor:.2f}), "
            f"total_factor={total_factor:.2f}"
        )

        return suggested_size

    def _analyze_cpu_factor(self, cpu_usage: float) -> float:
        """
        分析CPU使用率因子

        Args:
            cpu_usage: CPU使用率

        Returns:
            调整因子（>1表示可以增大批次，<1表示应该减小批次）
        """
        if cpu_usage < self.scale_up_cpu_threshold:
            # CPU使用率低，可以增大批次
            return 1.0 + (self.scale_up_cpu_threshold - cpu_usage) / self.scale_up_cpu_threshold
        elif cpu_usage > self.scale_down_cpu_threshold:
            # CPU使用率高，应该减小批次
            return max(0.5, 1.0 - (cpu_usage - self.scale_down_cpu_threshold) / (100 - self.scale_down_cpu_threshold))
        else:
            # CPU使用率正常
            return 1.0

    def _analyze_memory_factor(self, memory_usage: float) -> float:
        """
        分析内存使用率因子

        Args:
            memory_usage: 内存使用率

        Returns:
            调整因子
        """
        if memory_usage < self.scale_up_memory_threshold:
            # 内存使用率低，可以增大批次
            return 1.0 + (self.scale_up_memory_threshold - memory_usage) / self.scale_up_memory_threshold * 0.5
        elif memory_usage > self.scale_down_memory_threshold:
            # 内存使用率高，应该减小批次
            return max(
                0.3, 1.0 - (memory_usage - self.scale_down_memory_threshold) / (100 - self.scale_down_memory_threshold)
            )
        else:
            # 内存使用率正常
            return 1.0

    def _analyze_success_rate_factor(self, success_rate: float) -> float:
        """
        分析成功率因子

        Args:
            success_rate: 成功率（0.0-1.0）

        Returns:
            调整因子
        """
        if success_rate < 0.9:
            # 成功率低，应该减小批次
            return max(0.5, success_rate / 0.9)
        elif success_rate >= 0.98:
            # 成功率很高，可以适当增大批次
            return 1.1
        else:
            # 成功率正常
            return 1.0

    def _analyze_processing_time_factor(self, avg_processing_time: float) -> float:
        """
        分析处理时间因子

        Args:
            avg_processing_time: 平均处理时间（秒）

        Returns:
            调整因子
        """
        time_threshold = getattr(settings, "RULE_REGISTRATION_PERFORMANCE_AVG_TIME_THRESHOLD", 5.0)

        if avg_processing_time > time_threshold * 1.5:
            # 处理时间过长，应该减小批次
            return max(0.6, time_threshold / avg_processing_time)
        elif avg_processing_time < time_threshold * 0.5:
            # 处理时间很短，可以增大批次
            return 1.2
        else:
            # 处理时间正常
            return 1.0

    def adjust_based_on_performance(
        self, batch_size: int, processing_time: float, success: bool, force: bool = False
    ) -> bool:
        """
        根据任务执行结果调整批次大小策略

        Args:
            batch_size: 执行的批次大小
            processing_time: 处理时间
            success: 是否成功
            force: 是否强制调整（忽略调整间隔限制）

        Returns:
            是否进行了调整
        """
        if not self.enabled:
            return False

        current_time = time.perf_counter()

        # 检查调整间隔（除非强制调整）
        if not force and (current_time - self.stats.last_adjustment_time) < self.adjustment_interval:
            return False

        # 计算最优批次大小
        optimal_size = self.calculate_optimal_batch_size()

        # 检查是否需要调整
        if optimal_size == self.current_batch_size:
            return False

        # 执行调整
        return self._perform_adjustment(optimal_size, current_time)

    def _perform_adjustment(self, new_batch_size: int, current_time: float) -> bool:
        """
        执行批次大小调整

        Args:
            new_batch_size: 新的批次大小
            current_time: 当前时间

        Returns:
            是否成功调整
        """
        with self._lock:
            old_size = self.current_batch_size

            # 确保新批次大小在有效范围内
            new_batch_size = max(self.min_batch_size, min(self.max_batch_size, new_batch_size))

            if new_batch_size == old_size:
                return False

            # 更新批次大小
            self.current_batch_size = new_batch_size

            # 确定调整原因
            if new_batch_size > old_size:
                reason = "scale_up"
                self.stats.scale_up_count += 1
            else:
                reason = "scale_down"
                self.stats.scale_down_count += 1

            # 获取当前性能指标用于记录
            system_metrics = performance_monitor.get_current_metrics()
            registration_stats = performance_monitor.get_registration_performance_stats(window_size=10)

            cpu_usage = system_metrics.cpu_usage if system_metrics else 0.0
            memory_usage = system_metrics.memory_usage if system_metrics else 0.0
            success_rate = registration_stats.success_rate if registration_stats else 1.0
            avg_processing_time = registration_stats.avg_processing_time if registration_stats else 0.0

            # 记录调整历史
            adjustment_record = BatchAdjustmentRecord(
                timestamp=current_time,
                old_batch_size=old_size,
                new_batch_size=new_batch_size,
                reason=reason,
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                success_rate=success_rate,
                avg_processing_time=avg_processing_time,
            )
            self.adjustment_history.append(adjustment_record)

            # 更新统计信息
            self.stats.current_batch_size = new_batch_size
            self.stats.total_adjustments += 1
            self.stats.last_adjustment_time = current_time

            # 计算平均批次大小
            if self.adjustment_history:
                total_size = sum(record.new_batch_size for record in self.adjustment_history)
                self.stats.avg_batch_size = total_size / len(self.adjustment_history)

            # 计算调整频率（次/小时）
            if len(self.adjustment_history) > 1:
                time_span = self.adjustment_history[-1].timestamp - self.adjustment_history[0].timestamp
                if time_span > 0:
                    self.stats.adjustment_frequency = len(self.adjustment_history) / (time_span / 3600)

            logger.info(
                f"Batch size adjusted: {old_size} -> {new_batch_size} ({reason}), "
                f"CPU: {cpu_usage:.1f}%, Memory: {memory_usage:.1f}%, "
                f"Success rate: {success_rate:.2%}"
            )

            return True

    def get_adjustment_stats(self) -> BatchSizeStats:
        """
        获取批次大小调整统计信息

        Returns:
            批次大小统计信息
        """
        with self._lock:
            return BatchSizeStats(
                current_batch_size=self.stats.current_batch_size,
                min_batch_size=self.stats.min_batch_size,
                max_batch_size=self.stats.max_batch_size,
                total_adjustments=self.stats.total_adjustments,
                scale_up_count=self.stats.scale_up_count,
                scale_down_count=self.stats.scale_down_count,
                last_adjustment_time=self.stats.last_adjustment_time,
                avg_batch_size=self.stats.avg_batch_size,
                adjustment_frequency=self.stats.adjustment_frequency,
            )

    def get_adjustment_history(self, limit: int | None = None) -> list[BatchAdjustmentRecord]:
        """
        获取批次大小调整历史记录

        Args:
            limit: 返回记录数量限制，None表示返回所有记录

        Returns:
            调整历史记录列表
        """
        with self._lock:
            history = list(self.adjustment_history)
            if limit is not None and limit > 0:
                history = history[-limit:]
            return history

    def force_batch_size(self, batch_size: int, reason: str = "manual") -> bool:
        """
        强制设置批次大小

        Args:
            batch_size: 新的批次大小
            reason: 调整原因

        Returns:
            是否成功设置
        """
        if not (self.min_batch_size <= batch_size <= self.max_batch_size):
            logger.warning(
                f"Invalid batch size {batch_size}, must be between "
                f"{self.min_batch_size} and {self.max_batch_size}"
            )
            return False

        current_time = time.perf_counter()
        return self._perform_adjustment(batch_size, current_time)

    def reset_stats(self):
        """重置统计信息"""
        with self._lock:
            self.stats.total_adjustments = 0
            self.stats.scale_up_count = 0
            self.stats.scale_down_count = 0
            self.stats.last_adjustment_time = 0.0
            self.stats.avg_batch_size = float(self.current_batch_size)
            self.stats.adjustment_frequency = 0.0
            self.adjustment_history.clear()

        logger.info("Adaptive batch size manager stats reset")

    def get_optimization_recommendations(self) -> dict[str, any]:
        """
        获取批次大小优化建议

        Returns:
            优化建议字典
        """
        if not self.enabled:
            return {"enabled": False, "message": "Adaptive batch size management is disabled"}

        stats = self.get_adjustment_stats()
        recent_history = self.get_adjustment_history(limit=10)

        recommendations = {
            "enabled": True,
            "current_batch_size": stats.current_batch_size,
            "stats_summary": {
                "total_adjustments": stats.total_adjustments,
                "scale_up_count": stats.scale_up_count,
                "scale_down_count": stats.scale_down_count,
                "avg_batch_size": stats.avg_batch_size,
                "adjustment_frequency": stats.adjustment_frequency,
            },
            "recommendations": [],
        }

        # 分析调整频率
        if stats.adjustment_frequency > 10:  # 每小时超过10次调整
            recommendations["recommendations"].append(
                {
                    "type": "adjustment_frequency",
                    "priority": "medium",
                    "message": 
                        f"调整频率过高 ({stats.adjustment_frequency:.1f} 次/小时)，"
                        f"建议增大调整间隔或优化阈值设置",
                    "current_value": stats.adjustment_frequency,
                }
            )

        # 分析调整趋势
        if len(recent_history) >= 5:
            recent_scale_ups = sum(1 for record in recent_history if record.reason == "scale_up")
            recent_scale_downs = sum(1 for record in recent_history if record.reason == "scale_down")

            if recent_scale_ups > recent_scale_downs * 2:
                recommendations["recommendations"].append(
                    {
                        "type": "scale_trend",
                        "priority": "low",
                        "message": "最近批次大小持续增大，系统负载较低，性能表现良好",
                        "trend": "scale_up",
                    }
                )
            elif recent_scale_downs > recent_scale_ups * 2:
                recommendations["recommendations"].append(
                    {
                        "type": "scale_trend",
                        "priority": "medium",
                        "message": "最近批次大小持续减小，系统负载较高，建议检查系统资源",
                        "trend": "scale_down",
                    }
                )

        # 分析批次大小范围
        if stats.current_batch_size == self.min_batch_size and stats.scale_down_count > stats.scale_up_count:
            recommendations["recommendations"].append(
                {
                    "type": "batch_range",
                    "priority": "medium",
                    "message": f"批次大小经常达到最小值 ({self.min_batch_size})，建议降低最小批次大小限制",
                    "current_min": self.min_batch_size,
                    "suggested_min": max(20, self.min_batch_size - 20),
                }
            )
        elif stats.current_batch_size == self.max_batch_size and stats.scale_up_count > stats.scale_down_count:
            recommendations["recommendations"].append(
                {
                    "type": "batch_range",
                    "priority": "low",
                    "message": f"批次大小经常达到最大值 ({self.max_batch_size})，可以考虑提高最大批次大小限制",
                    "current_max": self.max_batch_size,
                    "suggested_max": min(1000, self.max_batch_size + 100),
                }
            )

        # 如果没有建议，添加正常状态信息
        if not recommendations["recommendations"]:
            recommendations["recommendations"].append(
                {"type": "status", "priority": "info", "message": "自适应批次大小管理运行正常，无需特殊调整"}
            )

        return recommendations


# 全局自适应批次大小管理器实例
adaptive_batch_manager = AdaptiveBatchSizeManager()
