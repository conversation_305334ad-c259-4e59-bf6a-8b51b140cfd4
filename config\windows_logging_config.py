"""
Windows 优化的日志配置模块

专门针对 Windows 平台的文件锁定问题进行优化，提供更好的多进程日志支持。

作者: 系统架构师
版本: 1.0.0
"""

import os
import platform
import time
from typing import Any

from .logging_config import UnifiedLogConfig
from .settings import settings


class WindowsOptimizedLogConfig(UnifiedLogConfig):
    """
    Windows 优化的日志配置类

    针对 Windows 平台的文件锁定问题进行特殊优化：
    1. 增加重试机制
    2. 优化轮转参数
    3. 添加进程标识
    4. 改进错误处理
    """

    def __init__(self):
        super().__init__()
        self.is_windows = platform.system().lower() == 'windows'
        self.process_id = os.getpid()

    def get_file_config(self) -> dict[str, Any]:
        """获取 Windows 优化的文件输出配置"""
        base_config = super().get_file_config()

        if self.is_windows:
            # Windows 特定优化
            base_config.update({
                # 增加轮转大小，减少轮转频率
                "rotation": self._get_optimized_rotation(),
                # 添加重试机制
                "catch": True,  # 捕获日志错误，避免程序崩溃
                # 优化队列参数
                "enqueue": True,
                "serialize": False,  # 禁用序列化以提高性能
            })

        return base_config

    def _get_optimized_rotation(self) -> str:
        """获取优化的轮转大小"""
        configured_rotation = settings.LOG_ROTATION

        # 如果配置的轮转大小太小，自动调整
        if self.is_windows and self._is_rotation_too_small(configured_rotation):
            optimized_size = "10 MB"  # Windows 下最小推荐 10MB
            print(
                f"警告: Windows 下轮转大小 {configured_rotation} 可能导致文件锁定问题，"
                f"已自动调整为 {optimized_size}"
            )
            return optimized_size

        return configured_rotation

    def _is_rotation_too_small(self, rotation: str) -> bool:
        """检查轮转大小是否太小"""
        try:
            # 解析轮转大小
            parts = rotation.strip().split()
            if len(parts) == 2:
                size_value = float(parts[0])
                size_unit = parts[1].upper()

                # 转换为 MB
                if size_unit == 'KB':
                    size_mb = size_value / 1024
                elif size_unit == 'MB':
                    size_mb = size_value
                elif size_unit == 'GB':
                    size_mb = size_value * 1024
                else:
                    return False

                # Windows 下小于 5MB 认为太小
                return size_mb < 5.0
        except (ValueError, IndexError):
            pass

        return False

    def get_windows_safe_path(self) -> str:
        """获取 Windows 安全的日志路径"""
        base_path = settings.LOG_PATH

        if self.is_windows:
            # 为不同进程使用不同的日志文件，避免冲突
            path_parts = base_path.rsplit('.', 1)
            if len(path_parts) == 2:
                name, ext = path_parts
                # 添加进程 ID 以避免冲突
                safe_path = f"{name}_{self.process_id}.{ext}"
                return safe_path

        return base_path

    def build_config(self) -> dict[str, Any]:
        """构建 Windows 优化的完整配置"""
        config = super().build_config()

        if self.is_windows:
            # 更新文件路径
            file_config = config.get("file_sink", {})
            file_config["path"] = self.get_windows_safe_path()
            config["file_sink"] = file_config

            # 添加 Windows 特定配置
            config.update({
                "windows_optimized": True,
                "process_id": self.process_id,
                "platform": "windows",
            })

        return config


class WindowsLogRotationHandler:
    """
    Windows 日志轮转处理器

    提供重试机制和错误恢复功能
    """

    def __init__(self, max_retries: int = 3, retry_delay: float = 0.1):
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def safe_rotation(self, old_path: str, new_path: str) -> bool:
        """
        安全的文件轮转，带重试机制

        Args:
            old_path: 原文件路径
            new_path: 新文件路径

        Returns:
            bool: 轮转是否成功
        """
        for attempt in range(self.max_retries):
            try:
                os.rename(old_path, new_path)
                return True
            except PermissionError as e:
                if attempt < self.max_retries - 1:
                    print(f"日志轮转重试 {attempt + 1}/{self.max_retries}: {e}")
                    time.sleep(self.retry_delay * (2 ** attempt))  # 指数退避
                else:
                    print(f"日志轮转失败，已重试 {self.max_retries} 次: {e}")
                    return False
            except Exception as e:
                print(f"日志轮转异常: {e}")
                return False

        return False


def get_platform_optimized_config():
    """
    获取平台优化的日志配置

    Returns:
        UnifiedLogConfig: 适合当前平台的日志配置
    """
    if platform.system().lower() == 'windows':
        return WindowsOptimizedLogConfig()
    else:
        # Linux/Unix 使用标准配置
        return UnifiedLogConfig()


def is_windows_platform() -> bool:
    """检查是否为 Windows 平台"""
    return platform.system().lower() == 'windows'


def get_recommended_rotation_size() -> str:
    """获取推荐的轮转大小"""
    if is_windows_platform():
        return "10 MB"  # Windows 推荐较大的轮转大小
    else:
        return "5 MB"   # Linux 可以使用较小的轮转大小


def print_platform_recommendations():
    """打印平台特定的建议"""
    if is_windows_platform():
        print("Windows 平台日志配置建议:")
        print("1. 轮转大小建议 >= 10MB，避免频繁轮转")
        print("2. 启用 enqueue=True 进行异步处理")
        print("3. 考虑为不同进程使用不同的日志文件")
        print("4. 监控日志错误，及时处理文件锁定问题")
    else:
        print("Linux/Unix 平台日志配置:")
        print("1. 可以使用较小的轮转大小 (>=5MB)")
        print("2. 文件锁定问题较少，配置更灵活")
        print("3. 多进程共享日志文件通常没有问题")


# 便捷函数
def get_optimized_log_config():
    """获取优化的日志配置（向后兼容）"""
    return get_platform_optimized_config()
