"""
规则分组工具
根据规则复杂度和执行时间智能分组，优化并行执行效率
"""

from dataclasses import dataclass
from enum import Enum

from core.logging.logging_system import log as logger
from core.rule_cache import RULE_CACHE


class RuleComplexity(Enum):
    """规则复杂度级别"""

    LIGHT = "light"  # 轻量级：<1ms
    MEDIUM = "medium"  # 中等：1-10ms
    HEAVY = "heavy"  # 重量级：>10ms


@dataclass
class RuleProfile:
    """规则性能档案"""

    rule_id: str
    complexity: RuleComplexity
    avg_execution_time: float  # 平均执行时间（毫秒）
    memory_usage: int  # 内存使用量（KB）
    cpu_intensive: bool  # 是否CPU密集型
    data_dependent: bool  # 是否依赖大量数据


@dataclass
class RuleGroup:
    """规则分组"""

    group_id: int
    rule_ids: list[str]
    estimated_time: float  # 预估执行时间（毫秒）
    complexity_mix: dict[RuleComplexity, int]  # 复杂度分布
    total_rules: int


class RuleGroupingStrategy:
    """
    规则分组策略
    根据规则特性和系统资源智能分组，优化并行执行效率
    """

    def __init__(self):
        """初始化分组策略"""
        self.rule_profiles: dict[str, RuleProfile] = {}
        self.execution_history: dict[str, list[float]] = {}  # 规则执行时间历史
        self.max_history_size = 100

        # 默认规则复杂度映射（基于规则类型）
        self.default_complexity_map = {
            # 性别限制类规则 - 轻量级
            "drug_limit_male": RuleComplexity.LIGHT,
            "drug_limit_female": RuleComplexity.LIGHT,
            # 年龄限制类规则 - 轻量级
            "age_limit": RuleComplexity.LIGHT,
            "pediatric_limit": RuleComplexity.LIGHT,
            # 药品相互作用 - 中等
            "drug_interaction": RuleComplexity.MEDIUM,
            "contraindication": RuleComplexity.MEDIUM,
            # 用量限制 - 中等
            "dosage_limit": RuleComplexity.MEDIUM,
            "frequency_limit": RuleComplexity.MEDIUM,
            # 复杂计算类 - 重量级
            "medication_days_limit": RuleComplexity.HEAVY,
            "total_cost_limit": RuleComplexity.HEAVY,
            "complex_diagnosis": RuleComplexity.HEAVY,
        }

        logger.debug("RuleGroupingStrategy initialized")

    def analyze_rule_complexity(self, rule_id: str) -> RuleProfile:
        """
        分析规则复杂度

        Args:
            rule_id: 规则ID

        Returns:
            规则性能档案
        """
        # 从缓存中获取规则实例
        rule_instance = RULE_CACHE.get(rule_id)
        if not rule_instance:
            # 默认为中等复杂度
            return RuleProfile(
                rule_id=rule_id,
                complexity=RuleComplexity.MEDIUM,
                avg_execution_time=5.0,
                memory_usage=100,
                cpu_intensive=True,
                data_dependent=True,
            )

        # 基于规则类型判断复杂度
        rule_class_name = rule_instance.__class__.__name__.lower()
        complexity = RuleComplexity.MEDIUM  # 默认

        # 匹配规则类型
        for pattern, comp in self.default_complexity_map.items():
            if pattern in rule_class_name or pattern in rule_id.lower():
                complexity = comp
                break

        # 基于历史执行时间调整
        if rule_id in self.execution_history:
            avg_time = sum(self.execution_history[rule_id]) / len(self.execution_history[rule_id])
            if avg_time < 1.0:
                complexity = RuleComplexity.LIGHT
            elif avg_time > 10.0:
                complexity = RuleComplexity.HEAVY

        # 估算内存使用和CPU密集度
        memory_usage = self._estimate_memory_usage(rule_instance)
        cpu_intensive = self._is_cpu_intensive(rule_instance)
        data_dependent = self._is_data_dependent(rule_instance)

        return RuleProfile(
            rule_id=rule_id,
            complexity=complexity,
            avg_execution_time=self._get_avg_execution_time(rule_id, complexity),
            memory_usage=memory_usage,
            cpu_intensive=cpu_intensive,
            data_dependent=data_dependent,
        )

    def _estimate_memory_usage(self, rule_instance) -> int:
        """估算规则内存使用量（KB）"""
        # 基于规则属性估算内存使用
        base_memory = 50  # 基础内存使用

        # 检查是否有大量数据属性
        if hasattr(rule_instance, "yb_codes") and rule_instance.yb_codes:
            base_memory += len(rule_instance.yb_codes) * 2  # 每个编码约2KB

        if hasattr(rule_instance, "diagnosis_codes") and rule_instance.diagnosis_codes:
            base_memory += len(rule_instance.diagnosis_codes) * 2

        return min(base_memory, 1000)  # 最大1MB

    def _is_cpu_intensive(self, rule_instance) -> bool:
        """判断是否CPU密集型"""
        # 基于规则类型判断
        class_name = rule_instance.__class__.__name__.lower()

        # CPU密集型规则模式
        cpu_intensive_patterns = [
            "calculation", "compute", "complex", "algorithm",
            "days_limit", "cost_limit", "interaction",
        ]

        return any(pattern in class_name for pattern in cpu_intensive_patterns)

    def _is_data_dependent(self, rule_instance) -> bool:
        """判断是否依赖大量数据"""
        # 检查是否需要遍历大量费用项或诊断
        class_name = rule_instance.__class__.__name__.lower()

        # 数据依赖型规则模式
        data_dependent_patterns = ["total", "sum", "count", "days", "frequency", "interaction"]

        return any(pattern in class_name for pattern in data_dependent_patterns)

    def _get_avg_execution_time(self, rule_id: str, complexity: RuleComplexity) -> float:
        """获取平均执行时间"""
        if rule_id in self.execution_history:
            return sum(self.execution_history[rule_id]) / len(self.execution_history[rule_id])

        # 基于复杂度的默认时间
        default_times = {RuleComplexity.LIGHT: 0.5, RuleComplexity.MEDIUM: 3.0, RuleComplexity.HEAVY: 15.0}

        return default_times[complexity]

    def record_execution_time(self, rule_id: str, execution_time: float):
        """记录规则执行时间"""
        if rule_id not in self.execution_history:
            self.execution_history[rule_id] = []

        self.execution_history[rule_id].append(execution_time)

        # 限制历史记录大小
        if len(self.execution_history[rule_id]) > self.max_history_size:
            self.execution_history[rule_id].pop(0)

    def group_rules_by_complexity(self, rule_ids: list[str], target_groups: int = None) -> list[RuleGroup]:
        """
        按复杂度分组规则

        Args:
            rule_ids: 规则ID列表
            target_groups: 目标分组数，None表示自动计算

        Returns:
            规则分组列表
        """
        if not rule_ids:
            return []

        # 分析所有规则的复杂度
        profiles = {}
        for rule_id in rule_ids:
            if rule_id not in self.rule_profiles:
                self.rule_profiles[rule_id] = self.analyze_rule_complexity(rule_id)
            profiles[rule_id] = self.rule_profiles[rule_id]

        # 按复杂度分类
        complexity_groups = {RuleComplexity.LIGHT: [], RuleComplexity.MEDIUM: [], RuleComplexity.HEAVY: []}

        for rule_id, profile in profiles.items():
            complexity_groups[profile.complexity].append(rule_id)

        # 计算目标分组数
        if target_groups is None:
            target_groups = min(len(rule_ids), 8)  # 默认最多8组

        # 生成平衡的分组
        groups = self._create_balanced_groups(complexity_groups, target_groups, profiles)

        logger.debug(f"Grouped {len(rule_ids)} rules into {len(groups)} groups")

        return groups

    def _create_balanced_groups(
        self,
        complexity_groups: dict[RuleComplexity, list[str]],
        target_groups: int,
        profiles: dict[str, RuleProfile],
    ) -> list[RuleGroup]:
        """创建平衡的规则分组"""

        groups = []
        group_id = 0

        # 首先处理重量级规则（每个单独一组或小组）
        heavy_rules = complexity_groups[RuleComplexity.HEAVY]
        for i in range(0, len(heavy_rules), 2):  # 每组最多2个重量级规则
            group_rules = heavy_rules[i : i + 2]
            groups.append(self._create_rule_group(group_id, group_rules, profiles))
            group_id += 1

        # 处理中等复杂度规则
        medium_rules = complexity_groups[RuleComplexity.MEDIUM]
        medium_group_size = max(1, len(medium_rules) // max(1, target_groups - len(groups)))

        for i in range(0, len(medium_rules), medium_group_size):
            group_rules = medium_rules[i : i + medium_group_size]
            groups.append(self._create_rule_group(group_id, group_rules, profiles))
            group_id += 1

        # 处理轻量级规则（可以大组）
        light_rules = complexity_groups[RuleComplexity.LIGHT]
        remaining_groups = max(1, target_groups - len(groups))
        light_group_size = max(1, len(light_rules) // remaining_groups)

        for i in range(0, len(light_rules), light_group_size):
            group_rules = light_rules[i : i + light_group_size]
            if group_rules:  # 确保组不为空
                groups.append(self._create_rule_group(group_id, group_rules, profiles))
                group_id += 1

        return groups

    def _create_rule_group(self, group_id: int, rule_ids: list[str], profiles: dict[str, RuleProfile]) -> RuleGroup:
        """创建规则分组"""
        if not rule_ids:
            return RuleGroup(group_id=group_id, rule_ids=[], estimated_time=0.0, complexity_mix={}, total_rules=0)

        # 计算预估执行时间
        estimated_time = sum(profiles[rule_id].avg_execution_time for rule_id in rule_ids)

        # 统计复杂度分布
        complexity_mix = {RuleComplexity.LIGHT: 0, RuleComplexity.MEDIUM: 0, RuleComplexity.HEAVY: 0}

        for rule_id in rule_ids:
            complexity_mix[profiles[rule_id].complexity] += 1

        return RuleGroup(
            group_id=group_id,
            rule_ids=rule_ids,
            estimated_time=estimated_time,
            complexity_mix=complexity_mix,
            total_rules=len(rule_ids),
        )

    def optimize_groups_for_parallel_execution(self, groups: list[RuleGroup]) -> list[RuleGroup]:
        """优化分组以提高并行执行效率"""
        if len(groups) <= 1:
            return groups

        # 按预估执行时间排序
        groups.sort(key=lambda g: g.estimated_time, reverse=True)

        # 尝试平衡各组的执行时间
        target_time = sum(g.estimated_time for g in groups) / len(groups)

        optimized_groups = []
        current_group_rules = []
        current_time = 0.0
        group_id = 0

        for group in groups:
            if current_time + group.estimated_time <= target_time * 1.2 or not current_group_rules:
                # 可以合并到当前组
                current_group_rules.extend(group.rule_ids)
                current_time += group.estimated_time
            else:
                # 创建新组
                if current_group_rules:
                    optimized_groups.append(
                        RuleGroup(
                            group_id=group_id,
                            rule_ids=current_group_rules,
                            estimated_time=current_time,
                            complexity_mix={},  # 重新计算
                            total_rules=len(current_group_rules),
                        )
                    )
                    group_id += 1

                current_group_rules = group.rule_ids
                current_time = group.estimated_time

        # 添加最后一组
        if current_group_rules:
            optimized_groups.append(
                RuleGroup(
                    group_id=group_id,
                    rule_ids=current_group_rules,
                    estimated_time=current_time,
                    complexity_mix={},
                    total_rules=len(current_group_rules),
                )
            )

        return optimized_groups


# 全局规则分组策略实例
rule_grouping_strategy = RuleGroupingStrategy()
