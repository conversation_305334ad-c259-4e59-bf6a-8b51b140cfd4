from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from config.settings import settings
from core.db_pool_manager import DynamicConnectionPoolManager, set_pool_manager
from core.logging.logging_system import log as logger

# 全局变量，延迟初始化
engine = None
SessionFactory = None
pool_manager = None


def _get_database_url() -> str:
    """
    获取数据库连接URL，支持新的配置方式

    Returns:
        str: 数据库连接URL

    Raises:
        ValueError: 如果数据库配置缺失或无效
    """
    # 使用新的配置方法获取数据库URL
    database_url = settings.get_database_url()

    # --- 配置验证 ---
    if not database_url:
        is_valid, error_msg = settings.validate_database_config()
        if not is_valid:
            logger.critical(f"数据库配置错误: {error_msg}")
            raise ValueError(f"数据库配置错误: {error_msg}")
        else:
            logger.critical("无法构建数据库连接URL")
            raise ValueError("无法构建数据库连接URL")

    # 自动修正常见的异步驱动错误
    if "asyncmy" in database_url:
        logger.warning(
            "DATABASE_URL contains 'asyncmy', which is an async driver. "
            "Automatically switching to 'pymysql' for synchronous operation.",
        )
        database_url = database_url.replace("asyncmy", "pymysql")

    logger.info(f"Connecting to database with URL: {database_url}")
    return database_url


def initialize_database_engine():
    """
    初始化数据库引擎和连接池
    延迟初始化，只在需要时创建
    """
    global engine, SessionFactory, pool_manager

    if engine is not None:
        logger.debug("Database engine already initialized")
        return

    # 获取数据库连接URL
    database_url = _get_database_url()

    # Create a synchronous engine instance with enhanced connection pool configuration.
    engine = create_engine(
        database_url,
        # 连接池配置
        pool_size=settings.DB_POOL_SIZE,
        max_overflow=settings.DB_MAX_OVERFLOW,
        pool_timeout=settings.DB_POOL_TIMEOUT,
        pool_recycle=settings.DB_POOL_RECYCLE,
        pool_pre_ping=settings.DB_POOL_PRE_PING,
        pool_reset_on_return=settings.DB_POOL_RESET_ON_RETURN,
        # 调试配置
        echo=False,
        echo_pool=True if settings.LOG_LEVEL == "DEBUG" else False,
    )

    # Create a sessionmaker factory for synchronous sessions.
    SessionFactory = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    # Initialize the dynamic connection pool manager
    pool_manager = DynamicConnectionPoolManager(engine)
    set_pool_manager(pool_manager)

    logger.debug(
        f"Database connection pool initialized: "
        f"pool_size={settings.DB_POOL_SIZE}, "
        f"max_overflow={settings.DB_MAX_OVERFLOW}, "
        f"timeout={settings.DB_POOL_TIMEOUT}s",
    )


def get_engine():
    """
    获取数据库引擎，如果未初始化则先初始化

    Returns:
        Engine: SQLAlchemy引擎实例
    """
    if engine is None:
        initialize_database_engine()
    return engine


def get_session_factory():
    """
    获取会话工厂，如果未初始化则先初始化

    Returns:
        sessionmaker: SQLAlchemy会话工厂
    """
    if SessionFactory is None:
        initialize_database_engine()
    return SessionFactory


def get_db_session():
    """
    Dependency injector that provides a database session.
    It ensures that the session is properly closed after the request is handled.
    """
    session_factory = get_session_factory()
    db = session_factory()
    try:
        yield db
    finally:
        db.close()


def start_db_pool_monitoring():
    """启动数据库连接池监控"""
    if pool_manager:
        pool_manager.start_monitoring()
        # 预热连接池
        if settings.DB_POOL_WARMUP_CONNECTIONS > 0:
            pool_manager.warm_up_pool()
        logger.info("Database connection pool monitoring started")
    else:
        logger.warning("Pool manager not initialized, cannot start monitoring")


def stop_db_pool_monitoring():
    """停止数据库连接池监控"""
    if pool_manager:
        pool_manager.graceful_shutdown()
        logger.info("Database connection pool monitoring stopped")
    else:
        logger.warning("Pool manager not initialized, cannot stop monitoring")


def get_db_pool_status():
    """获取数据库连接池状态"""
    if pool_manager:
        return pool_manager.get_pool_status()
    else:
        return {"error": "Pool manager not initialized"}
