"""
增强的日志格式化器
提供智能的堆栈信息显示功能

功能特性：
- 智能过滤器，自动为 ERROR 级别添加堆栈信息
- 保持原有日志格式的美观性
- 错误处理和降级机制
- 性能优化的处理流程
"""

import time
from typing import Any

from .stack_enhancer import stack_enhancer


def enhanced_log_filter(record) -> bool:
    """
    增强的日志过滤器
    自动为 ERROR 及以上级别的日志添加堆栈信息

    这个函数作为 loguru 的 filter 参数使用，会在每条日志记录时被调用。
    它负责检测是否需要添加堆栈信息，并调用堆栈增强器进行处理。

    Args:
        record: loguru 日志记录对象，包含所有日志相关信息

    Returns:
        bool: 始终返回 True，表示允许记录这条日志
    """
    try:
        # 记录处理开始时间（用于性能监控）
        start_time = time.perf_counter()

        # 使用堆栈增强器处理记录
        result = stack_enhancer.enhance_log_record(record)

        # 记录处理时间（仅在调试模式下）
        processing_time = time.perf_counter() - start_time
        if processing_time > 0.01:  # 如果处理时间超过10ms，记录警告
            # 避免在日志过滤器中再次触发日志，使用简单的标记
            record["extra"]["_stack_enhancement_slow"] = True
            record["extra"]["_processing_time"] = processing_time

        return result

    except Exception as e:
        # 如果增强失败，在消息中添加警告但不阻止日志记录
        # 这确保了即使堆栈增强器出现问题，原始日志仍然能够正常记录
        try:
            original_message = record.get("message", "")
            error_msg = str(e)
            record["message"] = f"{original_message}\n⚠️ 日志增强失败: {error_msg}"
        except Exception:
            # 如果连错误处理都失败了，至少确保日志能够记录
            pass

    # 始终返回 True，确保日志能够被记录
    return True


def create_enhanced_format() -> str:
    """
    创建增强的日志格式

    保持原有格式的美观性，但确保异常信息能够正确显示。
    这个格式与原始格式相同，因为堆栈信息是通过过滤器添加到消息中的。

    Returns:
        str: 日志格式字符串
    """
    return (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )


def create_console_enhanced_format() -> str:
    """
    创建控制台专用的增强格式

    为控制台输出提供更简洁的格式，同时保持堆栈信息的可读性。

    Returns:
        str: 控制台日志格式字符串
    """
    return "<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"


class LogEnhancementStats:
    """
    日志增强统计器

    用于监控日志增强功能的性能和使用情况。
    """

    def __init__(self):
        """初始化统计器"""
        self.total_processed = 0
        self.enhanced_count = 0
        self.error_count = 0
        self.total_processing_time = 0.0
        self.max_processing_time = 0.0
        self.start_time = time.time()

    def record_processing(self, processing_time: float, was_enhanced: bool, had_error: bool = False):
        """
        记录处理统计

        Args:
            processing_time: 处理时间（秒）
            was_enhanced: 是否进行了增强
            had_error: 是否发生错误
        """
        self.total_processed += 1
        self.total_processing_time += processing_time
        self.max_processing_time = max(self.max_processing_time, processing_time)

        if was_enhanced:
            self.enhanced_count += 1

        if had_error:
            self.error_count += 1

    def get_stats(self) -> dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        uptime = time.time() - self.start_time
        avg_processing_time = self.total_processing_time / self.total_processed if self.total_processed > 0 else 0

        return {
            "total_processed": self.total_processed,
            "enhanced_count": self.enhanced_count,
            "error_count": self.error_count,
            "enhancement_rate": (self.enhanced_count / self.total_processed if self.total_processed > 0 else 0),
            "error_rate": (self.error_count / self.total_processed if self.total_processed > 0 else 0),
            "avg_processing_time_ms": avg_processing_time * 1000,
            "max_processing_time_ms": self.max_processing_time * 1000,
            "uptime_seconds": uptime,
            "processing_rate_per_second": (self.total_processed / uptime if uptime > 0 else 0),
        }

    def reset_stats(self):
        """重置统计信息"""
        self.total_processed = 0
        self.enhanced_count = 0
        self.error_count = 0
        self.total_processing_time = 0.0
        self.max_processing_time = 0.0
        self.start_time = time.time()


# 全局统计器实例
enhancement_stats = LogEnhancementStats()


def get_enhancement_stats() -> dict[str, Any]:
    """
    获取日志增强统计信息

    Returns:
        Dict[str, Any]: 包含增强器和统计器的综合信息
    """
    try:
        return {
            "filter_stats": enhancement_stats.get_stats(),
            "stack_enhancer_stats": stack_enhancer.get_cache_stats(),
            "timestamp": time.time(),
        }
    except Exception as e:
        return {"error": f"获取统计信息失败: {str(e)}", "timestamp": time.time()}


def reset_enhancement_stats():
    """重置所有增强统计信息"""
    try:
        enhancement_stats.reset_stats()
        stack_enhancer.clear_cache()
    except Exception:
        pass


# 为了向后兼容，提供一些便捷函数
def is_enhancement_enabled() -> bool:
    """检查堆栈增强是否启用"""
    return stack_enhancer.enabled


def disable_enhancement():
    """临时禁用堆栈增强"""
    stack_enhancer.enabled = False


def enable_enhancement():
    """启用堆栈增强"""
    stack_enhancer.enabled = True
