"""
简化的日志系统模块

提供统一的日志系统，移除了复杂的环境特定配置，使用简化的统一配置。

作者: 系统架构师
版本: 2.0.0 (简化重构版)
"""

import platform
import sys
from typing import Any

from loguru import logger

from config.logging_config import LOGGING_CONFIG, get_log_config


class LoggingSystem:
    """
    简化的日志系统类

    使用统一的日志配置，支持所有环境的日志需求。
    移除了复杂的环境特定配置逻辑。
    """

    def __init__(self, config: dict[str, Any] | None = None):
        """
        初始化日志系统

        Args:
            config: 自定义日志配置字典，如果为None则使用统一配置
        """
        if config is None:
            # 使用统一配置
            self.log_config = get_log_config()
            self.config = self.log_config.build_config()
        else:
            # 使用自定义配置
            self.config = config
            self.log_config = None

        self._configure_logger()

    def _configure_logger(self):
        """配置日志系统"""
        # 清除现有的处理器
        for handler in logger._core.handlers:  # pylint: disable=protected-access # type: ignore
            logger.remove(handler)

        # 配置文件日志处理器
        self._configure_file_sink()

        # 配置控制台日志处理器
        self._configure_stdout_sink()

        # 日志配置完成

    def _configure_file_sink(self):
        """配置文件日志处理器"""
        try:
            # 导入增强格式化器
            from .enhanced_formatter import create_enhanced_format, enhanced_log_filter

            file_config = self.config.get("file_sink", {})

            # 使用文件配置或默认配置
            file_path = file_config.get("path", self.config.get("path", "logs/app.log"))
            file_rotation = file_config.get("rotation", self.config.get("rotation", "10 MB"))
            file_retention = file_config.get("retention", self.config.get("retention", "7 days"))
            file_compression = file_config.get("compression", self.config.get("compression", "gz"))

            # 使用增强的格式和过滤器
            sink_kwargs = {
                "sink": file_path,
                "format": create_enhanced_format(),  # 使用增强格式
                "filter": enhanced_log_filter,  # 添加堆栈增强过滤器
                "rotation": file_rotation,
                "retention": file_retention,
                "compression": file_compression,
                "enqueue": self.config.get("enqueue", True),
                "level": self.config.get("level", "INFO").upper(),
            }

            # Windows 特定优化
            if platform.system().lower() == "windows":
                sink_kwargs.update(
                    {
                        "catch": True,  # 捕获日志错误，避免程序崩溃
                        "serialize": False,  # 禁用序列化以提高性能
                    }
                )

            logger.add(**sink_kwargs)

        except ImportError as e:
            # 如果导入增强器失败，使用原始配置
            logger.warning(f"无法导入日志增强器，使用基础配置: {e}")
            self._configure_file_sink_fallback()
        except Exception as e:
            # 如果配置增强器失败，使用原始配置
            logger.error(f"配置日志增强器失败，使用基础配置: {e}")
            self._configure_file_sink_fallback()

    def _configure_file_sink_fallback(self):
        """配置文件日志处理器（降级版本）"""
        file_config = self.config.get("file_sink", {})

        # 使用文件配置或默认配置
        file_path = file_config.get("path", self.config.get("path", "logs/app.log"))
        file_rotation = file_config.get("rotation", self.config.get("rotation", "10 MB"))
        file_retention = file_config.get("retention", self.config.get("retention", "7 days"))
        file_compression = file_config.get("compression", self.config.get("compression", "gz"))

        # 使用原始配置
        sink_kwargs = {
            "sink": file_path,
            "format": self.config.get("format", "{time} | {level} | {message}"),
            "rotation": file_rotation,
            "retention": file_retention,
            "compression": file_compression,
            "enqueue": self.config.get("enqueue", True),
            "level": self.config.get("level", "INFO").upper(),
        }

        # Windows 特定优化
        if platform.system().lower() == "windows":
            sink_kwargs.update(
                {
                    "catch": True,  # 捕获日志错误，避免程序崩溃
                    "serialize": False,  # 禁用序列化以提高性能
                }
            )

        logger.add(**sink_kwargs)

    def _configure_stdout_sink(self):
        """配置控制台日志处理器"""
        stdout_config = self.config.get("stdout_sink", {})

        if stdout_config and stdout_config.get("enabled", False):
            try:
                # 导入增强格式化器
                from .enhanced_formatter import create_console_enhanced_format, enhanced_log_filter

                logger.add(
                    sink=sys.stdout,
                    format=create_console_enhanced_format(),  # 使用增强的控制台格式
                    filter=enhanced_log_filter,  # 添加堆栈增强过滤器
                    level=stdout_config.get("level", "INFO").upper(),
                    enqueue=self.config.get("enqueue", True),
                )
            except Exception:
                # 如果增强器失败，使用原始配置
                logger.add(
                    sink=sys.stdout,
                    format=stdout_config.get("format", "{time:HH:mm:ss} | {level} | {message}"),
                    level=stdout_config.get("level", "INFO").upper(),
                    enqueue=self.config.get("enqueue", True),
                )

    def get_logger(self):
        return logger

    def get_stack_enhancement_stats(self):
        """获取堆栈增强统计信息"""
        try:
            from .enhanced_formatter import get_enhancement_stats

            return get_enhancement_stats()
        except ImportError:
            return {"error": "堆栈增强功能未启用"}
        except Exception as e:
            return {"error": f"获取统计信息失败: {str(e)}"}

    def reset_stack_enhancement_stats(self):
        """重置堆栈增强统计信息"""
        try:
            from .enhanced_formatter import reset_enhancement_stats

            reset_enhancement_stats()
            return {"success": True, "message": "统计信息已重置"}
        except ImportError:
            return {"error": "堆栈增强功能未启用"}
        except Exception as e:
            return {"error": f"重置统计信息失败: {str(e)}"}

    def toggle_stack_enhancement(self, enabled: bool):
        """切换堆栈增强功能"""
        try:
            from .enhanced_formatter import disable_enhancement, enable_enhancement

            if enabled:
                enable_enhancement()
                return {"success": True, "message": "堆栈增强已启用"}
            else:
                disable_enhancement()
                return {"success": True, "message": "堆栈增强已禁用"}
        except ImportError:
            return {"error": "堆栈增强功能未安装"}
        except Exception as e:
            return {"error": f"切换堆栈增强失败: {str(e)}"}


# Global instance of the logging system and logger
# This makes the logger easily importable and usable across the application
# 使用简化的统一配置
_logging_system_instance = LoggingSystem()
log = _logging_system_instance.get_logger()  # Expose the configured logger directly


# Optional: A function to get the globally configured logger,
# which is just returning the global `log` variable.
def get_global_logger():
    """
    获取全局配置的日志器

    Returns:
        logger: 配置好的日志器实例
    """
    return log


def get_logging_system():
    """
    获取全局日志系统实例

    Returns:
        LoggingSystem: 日志系统实例
    """
    return _logging_system_instance


def reinitialize_logging_system(config: dict[str, Any] | None = None):
    """
    重新初始化日志系统

    Args:
        config: 自定义配置字典，如果为None则使用统一配置
    """
    global _logging_system_instance, log

    _logging_system_instance = LoggingSystem(config=config)
    log = _logging_system_instance.get_logger()


if __name__ == "__main__":
    # Example Usage (for testing purposes using the global logger)

    # The logger is already configured globally, so we can use `log` directly
    log.info("Logging system initialized with default config (from global instance).")
    log.debug("This is a debug message from global logger.")
    log.warning("This is a warning message from global logger.")
    log.error("This is an error message from global logger.")

    try:
        1 / 0  # noqa: B018
    except ZeroDivisionError:
        log.exception("An exception occurred (logged by global logger)!")

    # Accessing config for path display
    # Note: LOGGING_CONFIG is imported, so we can use it directly here for the example.
    print(f"Test logs should be in: {LOGGING_CONFIG['path'].split('/')[0]}")
    log.info(f"Log files are configured to be in: {LOGGING_CONFIG['path']}")
